# 系统架构和技术栈详解

## 目录
- [整体架构](#整体架构)
- [技术栈详解](#技术栈详解)
- [目录结构详解](#目录结构详解)
- [核心模块设计](#核心模块设计)
- [数据流向](#数据流向)
- [组件架构](#组件架构)

## 整体架构

### 架构图

```mermaid
graph TB
    A[用户界面层 - Vue3 Components] --> B[路由层 - Vue Router]
    B --> C[状态管理层 - Pinia Stores]
    C --> D[API服务层 - Axios HTTP]
    D --> E[后端服务 - Alert Converge API]
    
    F[工具层 - Utils/Directives] --> A
    G[国际化层 - Vue-i18n] --> A
    H[主题系统 - SCSS Themes] --> A
    
    subgraph "前端应用架构"
        A
        B
        C
        D
        F
        G
        H
    end
```

### 分层架构说明

1. **表现层 (Presentation Layer)**
   - Vue3组件 + Element Plus UI
   - 响应式布局系统
   - 主题切换和国际化

2. **业务逻辑层 (Business Logic Layer)**
   - Pinia状态管理
   - 路由守卫和权限控制
   - 业务规则处理

3. **数据访问层 (Data Access Layer)**
   - Axios HTTP客户端
   - API接口封装
   - 请求/响应拦截器

4. **基础设施层 (Infrastructure Layer)**
   - 工具函数库
   - 自定义指令
   - 全局配置

## 技术栈详解

### 核心框架

#### Vue 3.2.47
- **Composition API**: 更好的逻辑复用和类型推导
- **响应式系统**: Proxy-based响应式，性能更优
- **Tree-shaking**: 更小的打包体积
- **TypeScript支持**: 原生TypeScript支持

```typescript
// Composition API示例
import { ref, reactive, computed, onMounted } from 'vue'

export default defineComponent({
  setup() {
    const count = ref(0)
    const state = reactive({ name: 'Vue3' })
    
    const doubleCount = computed(() => count.value * 2)
    
    onMounted(() => {
      console.log('组件已挂载')
    })
    
    return { count, state, doubleCount }
  }
})
```

#### TypeScript 4.8.4
- **类型安全**: 编译时类型检查
- **智能提示**: 更好的IDE支持
- **接口定义**: 规范数据结构

```typescript
// 类型定义示例
interface ApiResponse<T> {
  code: string
  message: string
  data: T
}

interface AlertModel {
  id: number
  modelName: string
  modelCode: string
  businessType: string
  alertType: string
  description: string
}
```

#### Vite 4.1.4
- **快速启动**: 基于ESM的开发服务器
- **热更新**: 毫秒级热更新
- **构建优化**: Rollup打包，支持代码分割

### UI框架

#### Element Plus 2.9.5
- **组件丰富**: 60+高质量组件
- **主题定制**: 支持CSS变量定制
- **TypeScript**: 完整的类型定义

```vue
<template>
  <el-table :data="tableData" border stripe>
    <el-table-column prop="name" label="名称" />
    <el-table-column prop="status" label="状态">
      <template #default="{ row }">
        <el-tag :type="getTagType(row.status)">
          {{ row.status }}
        </el-tag>
      </template>
    </el-table-column>
  </el-table>
</template>
```

### 状态管理

#### Pinia 2.0.32
- **Vue3官方推荐**: 替代Vuex的新一代状态管理
- **TypeScript友好**: 完整的类型推导
- **模块化设计**: 自动代码分割

```typescript
// Store定义示例
export const useAlertStore = defineStore('alert', {
  state: () => ({
    systemNameOptions: [] as SystemOption[],
    modelNameOptions: [] as ModelOption[]
  }),
  
  getters: {
    getSystemById: (state) => (id: number) => 
      state.systemNameOptions.find(item => item.id === id)
  },
  
  actions: {
    async fetchSystemNames() {
      const res = await WarnApi.getAllSystemName()
      if (res.code === '0000') {
        this.systemNameOptions = res.data
      }
    }
  }
})
```

### 路由管理

#### Vue Router 4.1.6
- **组合式API**: 支持Composition API
- **动态路由**: 支持动态添加路由
- **路由守卫**: 完整的导航守卫系统

```typescript
// 路由配置示例
const routes = [
  {
    path: '/early-warn',
    component: LayoutBox,
    meta: { title: '预警管理', requiresAuth: true },
    children: [
      {
        path: 'model',
        name: 'EarlyWarnModel',
        component: () => import('@/pages/EarlyWarnManagement/EarlyWarningModel/EarlyWarningModel.vue'),
        meta: { title: '预警模型', keepAlive: true }
      }
    ]
  }
]
```

### HTTP客户端

#### Axios 1.3.4
- **请求拦截**: 统一处理请求头和参数
- **响应拦截**: 统一处理响应和错误
- **取消请求**: 支持请求取消

```typescript
// HTTP配置示例
axios.interceptors.request.use(config => {
  const token = utils.getCookie('UserLoginToken')
  if (token) {
    config.headers.Token = token
  }
  return config
})

axios.interceptors.response.use(
  response => {
    const { code } = response.data
    if (code === 403) {
      ElMessage.error('token失效，请重新登录')
      router.push('/login')
    }
    return response
  },
  error => {
    ElMessage.error('网络请求失败')
    return Promise.reject(error)
  }
)
```

## 目录结构详解

### src目录结构

```
src/
├── api/                    # API接口层
│   ├── DataManagement.ts   # 数据管理API
│   ├── EarlyWarnManage.ts  # 预警管理API
│   └── User.ts             # 用户相关API
├── assets/                 # 静态资源
│   ├── css/               # 样式文件
│   │   ├── base.scss      # 基础样式
│   │   ├── mixin.scss     # 混入样式
│   │   └── theme/         # 主题相关
│   ├── font/              # 字体文件
│   ├── imgs/              # 图片资源
│   └── js/                # 工具函数
│       ├── http.ts        # HTTP封装
│       ├── utils.ts       # 工具函数
│       └── filters.ts     # 过滤器
├── components/             # 公共组件
│   ├── home/              # 首页组件
│   └── layoutBox/         # 布局组件
│       ├── index.vue      # 布局入口
│       └── components/    # 布局子组件
├── config/                # 配置文件
├── directives/            # 自定义指令
│   ├── index.ts           # 指令入口
│   └── hasPermissionDirective.ts  # 权限指令
├── i18n/                  # 国际化
│   ├── i18n.ts            # i18n配置
│   ├── index.ts           # 入口文件
│   └── langs/             # 语言包
├── pages/                 # 页面组件
│   ├── DataSouceManagement/     # 数据源管理
│   ├── EarlyWarnManagement/     # 预警管理
│   └── SystemManagement/        # 系统管理
├── router/                # 路由配置
│   └── index.ts           # 路由定义
├── stores/                # 状态管理
│   ├── index.ts           # 主Store
│   ├── lang.ts            # 语言Store
│   ├── menuStore.ts       # 菜单Store
│   ├── tags.ts            # 标签Store
│   └── theme.ts           # 主题Store
├── App.vue                # 根组件
├── main.ts                # 应用入口
└── globalSettings.ts      # 全局配置
```

### 关键文件说明

#### main.ts - 应用入口
```typescript
import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import router from './router'
import ElementPlus from 'element-plus'
import { setupI18n } from '@/i18n/i18n'
import directives from './directives'

const app = createApp(App)
app.use(directives)        // 全局指令
app.use(setupI18n)         // 国际化
app.use(createPinia())     // 状态管理
app.use(router)            # 路由
app.use(ElementPlus)       # UI组件库
app.mount('#app')
```

#### globalSettings.ts - 全局配置
```typescript
export const sysName = '预警后台管理系统'
export const sysDomainCode = 'warning-admin'
export const sysSsoApi = '/sso-service'
export const sysApi = '/alert-converge-api'

export const globalConfig = {
  setting: {
    defaultLang: 'zh',
    showLang: true,
    showFullScreen: true,
    showMessages: true,
    showSetUp: true
  },
  theme: {
    layout: localStorage.layout || 'vertical',
    themeName: localStorage.themeName || 'blueblack'
  }
}
```

## 核心模块设计

### 1. 布局系统

支持三种布局模式：
- **垂直布局**: 侧边栏 + 顶部导航
- **水平布局**: 顶部导航 + 内容区
- **列式布局**: 多列展示

### 2. 权限系统

基于指令的权限控制：
```typescript
// 权限指令
app.directive('has-permission', {
  mounted(el, binding) {
    const permission = binding.value
    if (!hasPermission(permission)) {
      el.remove()
    }
  }
})
```

### 3. 主题系统

支持多主题切换：
- 蓝黑主题 (blueblack)
- 自定义主题扩展

### 4. 国际化系统

支持中英文切换：
```typescript
const messages = {
  zh: { welcome: '欢迎' },
  en: { welcome: 'Welcome' }
}
```

## 数据流向

### 请求流程

```mermaid
sequenceDiagram
    participant C as Component
    participant S as Store
    participant A as API
    participant B as Backend
    
    C->>S: 调用action
    S->>A: 发起HTTP请求
    A->>B: 请求后端API
    B-->>A: 返回数据
    A-->>S: 处理响应
    S-->>C: 更新状态
    C->>C: 重新渲染
```

### 状态管理流程

1. **组件触发**: 用户操作触发组件方法
2. **调用Store**: 组件调用Store的action
3. **API请求**: Store通过API层发起HTTP请求
4. **数据处理**: 处理响应数据并更新state
5. **视图更新**: 组件响应状态变化重新渲染

## 组件架构

### 组件分类

1. **页面组件**: 完整的业务页面
2. **业务组件**: 特定业务逻辑的组件
3. **基础组件**: 通用UI组件
4. **布局组件**: 页面布局相关组件

### 组件通信

- **父子通信**: Props + Emit
- **跨组件通信**: Pinia Store
- **事件总线**: 自定义事件系统

### 组件设计原则

1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 组件设计考虑复用场景
3. **可维护性**: 清晰的代码结构和注释
4. **性能优化**: 合理使用缓存和懒加载
