import {defineStore} from 'pinia'
import WarnApi from '@/api/EarlyWarnManage'

export const store = defineStore('store', {
  state: () => {
    return {
      systemNameOptions: [], // 系统名称
      modelNameOptions: [], // 预警名称
      businessTypeOptions: [], // 业务类型
      alertTypeOptions: [], // 预警类型
    }
  },
  getters: {},
  actions: {
    // 获取系统名称列表
    async allSystemName () {
      const res = await WarnApi.getAllSystemName()
      if (res.code === '0000') {
        this.systemNameOptions = res.data
      }
    },
    // 获取预警名称列表
    async allModelName () {
      const res = await WarnApi.getAllModelName()
      if (res.code === '0000') {
        this.modelNameOptions = res.data
      }
    },
    // 获取业务类型列表
    async allBusinessType () {
      const res = await WarnApi.getSysDictValue({keyName: 'business_type'})
      if (res.code === '0000') {
        this.businessTypeOptions = res.data
      }
    },
    // 获取预警类型列表
    async allAlertType () {
      const res = await WarnApi.getSysDictValue({keyName: 'alert_type'})
      if (res.code === '0000') {
        this.alertTypeOptions = res.data
      }
    }
  }
})
