<template>
  <el-dialog :title="dialogType === 1 ? '新增预警模型' : '编辑预警模型'"
             v-model="state.visible"
             width="800"
             @close="resetDialog(ruleFormRef)">
    <el-form :model="state.form" :rules="rules" ref="ruleFormRef" label-width="140">
      <!-- 基础设置 -->
      <div class="section-title">
        <span>基础设置</span>
      </div>
      <el-form-item label="预警名称" prop="modelName">
        <el-input v-model="state.form.modelName" maxlength="40" placeholder="请输入"/>
      </el-form-item>
      <el-form-item label="模型编号" prop="modelCode">
        <el-input v-model="state.form.modelCode" placeholder="请输入" :disabled="dialogType === 2"/>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select v-model="state.form.businessType" placeholder="请选择">
          <el-option
            v-for="item in (store() as any).businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警类型" prop="alertType">
        <el-select v-model="state.form.alertType" placeholder="请选择">
          <el-option
            v-for="item in (store() as any).alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否关联规则" prop="related">
        <el-select v-model="state.form.related" placeholder="请选择" style="width: 120px;">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
        <el-select v-model="state.form.ruleId" placeholder="请选择" style="width: 120px; margin-left: 10px;">
          <el-option label="规则1" value="1" />
          <el-option label="规则2" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否生成预警消息" prop="generateMessage">
        <el-select v-model="state.form.generateMessage" placeholder="请选择" style="width: 120px;">
          <el-option label="是" :value="true" />
          <el-option label="否" :value="false" />
        </el-select>
      </el-form-item>

      <!-- 预警设置 -->
      <div class="section-title">
        <span >预警设置</span>
      </div>
      <el-form-item label="预警频率" prop="alertFrequency">
        <el-select v-model="state.form.frequencyType" placeholder="限制" style="width: 120px;">
          <el-option label="限制" value="limit" />
          <el-option label="不限制" value="unlimited" />
        </el-select>
        <el-input-number 
          v-model="state.form.frequencyCount" 
          :min="1" 
          :max="100"
          style="width: 120px; margin-left: 10px;"
        />
        <span style="margin-left: 10px;">次 每小时</span>
      </el-form-item>
      <el-form-item label="预警方式" prop="alertMethods">
        <el-checkbox v-model="state.form.dingTalkNotify">钉钉通知</el-checkbox>
        <el-checkbox v-model="state.form.smsNotify" style="margin-left: 20px;">短信通知</el-checkbox>
      </el-form-item>
      <el-form-item label="钉钉群Webhook地址" prop="dingTalkWebhook" v-if="state.form.dingTalkNotify">
        <el-input 
          v-model="state.form.dingTalkWebhook" 
          type="textarea" 
          :rows="3"
          placeholder="请输入钉钉群Webhook地址"
        />
      </el-form-item>
      <el-form-item label="预警通知人员" prop="notifiers">
        <el-button type="primary" size="small">请选择人员</el-button>
      </el-form-item>
      <el-form-item label="预警内容" prop="alertContent">
        <div class="alert-content-container">
          <div class="basic-variables">
            <div class="variables-title">基础变量</div>
            <div class="variables-list">
              <el-tag 
                v-for="variable in basicVariables" 
                :key="variable.key"
                @click="insertVariable(variable.key)"
                class="variable-tag"
              >
                {{ variable.label }}
              </el-tag>
            </div>
          </div>
          <div class="message-content">
            <div class="content-title">消息内容</div>
            <div class="selected-variables">
              <el-tag
                v-for="(tag, index) in state.selectedVariables"
                :key="index"
                closable
                @close="removeVariable(index)"
                class="selected-tag"
              >
                {{ tag.label }}
              </el-tag>
            </div>
            <el-input 
              v-model="state.form.messageContent" 
              type="textarea" 
              :rows="4"
              placeholder="请输入预警消息内容"
              style="margin-top: 10px;"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="模型说明" prop="description">
        <el-input 
          v-model="state.form.description" 
          type="textarea" 
          maxlength="200" 
          placeholder="请输入"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <div class="tc mt20">
      <el-button type="primary" @click="saveDialog(ruleFormRef)" :loading="state.btnLoading">保存</el-button>
      <el-button @click="$emit('update:dialogVisible', false)">取消</el-button>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { reactive, ref, watch } from 'vue'
import {ElMessage} from 'element-plus'
import type { FormRules, FormInstance } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'
import { store } from '@/stores'

// 参数管理
const props = defineProps<{
  dialogVisible: Boolean;
  dialogType: Number;
  dialogForm: Object;
}>();

// 基础变量列表
const basicVariables = [
  { key: 'thirdPartyName', label: '第三方名称+' },
  { key: 'period', label: '期次+' },
  { key: 'alertName', label: '预警名称+' },
  { key: 'businessType', label: '业务类型+' },
  { key: 'alertType', label: '预警类型+' },
  { key: 'triggerCondition', label: '触发条件+' },
  { key: 'reason', label: '原因+' }
]

// 状态管理
const state = reactive({
  visible: props.dialogVisible,
  form: {
    id: '',
    modelName: '', // 预警名称
    modelCode: '', // 模型编号
    businessType: '', // 业务类型
    alertType: '', // 预警类型
    related: '', // 是否关联规则
    ruleId: '', // 关联规则ID
    generateMessage: '', // 是否生成预警消息
    frequencyType: 'limit', // 预警频率类型
    frequencyCount: 5, // 预警频率次数
    dingTalkNotify: true, // 钉钉通知
    smsNotify: true, // 短信通知
    dingTalkWebhook: '', // 钉钉群Webhook地址
    notifiers: [], // 预警通知人员
    messageContent: '', // 预警消息内容
    description: '' // 模型说明
  },
  selectedVariables: [] as Array<{key: string, label: string}>, // 已选择的变量
  btnLoading: false
})

watch(
  () => props.dialogVisible,
  (newVal) => {
    state.visible = newVal
    if (newVal) {
      state.form = {...state.form, ...props.dialogForm}
    }
  }
)

const ruleFormRef = ref<FormInstance>()

const rules = reactive<FormRules>({
  modelName: [
    { required: true, message: '请输入预警名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9\u4e00-\u9fa5]+$/, message: '请输入中文、数字或字母', trigger: 'blur'}
  ],
  modelCode: [
    { required: true, message: '请输入模型编号', trigger: 'blur' },
    { pattern: /^\d+$/, message: '请输入数字', trigger: 'blur' },
    { max: 6, message: '限制6位数', trigger: 'blur' },
  ],
  businessType: [{ required: true, message: '请选择业务类型', trigger: 'change' }],
  alertType: [{ required: true, message: '请选择预警类型', trigger: 'change' }],
  related: [{ required: true, message: '请选择是否关联规则', trigger: 'change' }],
  generateMessage: [{ required: true, message: '请选择是否生成预警消息', trigger: 'change' }],
  frequencyCount: [{ required: true, message: '请输入预警频率', trigger: 'blur' }],
  dingTalkWebhook: [{ required: true, message: '请输入钉钉群Webhook地址', trigger: 'blur' }],
  messageContent: [{ required: true, message: '请输入预警消息内容', trigger: 'blur' }]
})

// 定义要发送的emit事件
let $emit = defineEmits(['update:dialogVisible', 'callback'])

// 插入变量到消息内容
const insertVariable = (variableKey: string) => {
  const variable = basicVariables.find(v => v.key === variableKey)
  if (variable) {
    // 检查是否已经添加过这个变量
    const isAlreadyAdded = state.selectedVariables.some(v => v.key === variableKey)
    if (!isAlreadyAdded) {
      state.selectedVariables.push({ key: variableKey, label: variable.label })
    }
  }
}

// 移除消息内容中的变量
const removeVariable = (index: number) => {
  const variable = state.selectedVariables[index];
  if (variable) {
    state.selectedVariables.splice(index, 1);
  }
};

// 弹窗保存
const saveDialog = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      state.btnLoading = true
      let apiName = 'addAlertModel'
      if (state.form.id) {
        apiName = 'editAlertModel'
      }
      (WarnApi as any)[apiName](state.form).then((res: any) => {
        state.btnLoading = false
        if (res.code === '0000') {
          state.visible = false
          $emit('callback')
          ElMessage({type: 'success', message: '操作成功'})
        } else {
          ElMessage({type: 'error', message: res.message})
        }
      })
    }
  })
}

// 重置弹窗
const resetDialog = (formEl: FormInstance | undefined) => {
  state.form = {
    id: '',
    modelName: '', // 预警名称
    modelCode: '', // 模型编号
    businessType: '', // 业务类型
    alertType: '', // 预警类型
    related: '', // 是否关联规则
    ruleId: '', // 关联规则ID
    generateMessage: '', // 是否生成预警消息
    frequencyType: 'limit', // 预警频率类型
    frequencyCount: 5, // 预警频率次数
    dingTalkNotify: true, // 钉钉通知
    smsNotify: true, // 短信通知
    dingTalkWebhook: '', // 钉钉群Webhook地址
    notifiers: [], // 预警通知人员
    messageContent: '', // 预警消息内容
    description: '' // 模型说明
  }
  state.selectedVariables = [] // 重置已选择的变量
  if (!formEl) return
  formEl.resetFields()
  $emit('update:dialogVisible', false)
}
</script>

<style scoped lang="scss">
.el-input {
  width: 260px;
}
.w260 {
  width: 260px;
}
.el-select {
  width: 260px;
}

.section-title {
  font-size: 16px;
  font-weight: bold;
  margin: 20px 0 15px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
}

.alert-content-container {
  display: flex;
  gap: 20px;
  width: 100%;
}

.basic-variables {
  width: 200px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 10px;
}

.variables-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.variables-list {
  max-height: 200px;
  overflow-y: auto;
}

.variable-tag {
  display: block;
  margin-bottom: 8px;
  cursor: pointer;
  background-color: #f5f7fa;
  border: 1px solid #e4e7ed;
  color: #606266;
  
  &:hover {
    background-color: #ecf5ff;
    border-color: #409eff;
    color: #409eff;
  }
}

.message-content {
  flex: 1;
}

.content-title {
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #606266;
}

.selected-variables {
  margin-bottom: 10px;
  min-height: 32px;
  padding: 5px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  background-color: #fafafa;
}

.selected-tag {
  margin: 2px;
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  
  &:hover {
    background-color: #66b1ff;
    border-color: #66b1ff;
  }
}

.tc {
  text-align: center;
}

.mt20 {
  margin-top: 20px;
}
</style>
