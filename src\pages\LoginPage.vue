<template>
  <div id="h-loginBox">
    <div class="loginBox">
      <el-form
        label-position="left"
        label-width="0px"
        ref="ruleFormRef"
        :model="ruleForm"
        :rules="rules"
        class="h-loginForm card-box loginform"
      >
        <h3 class="title flex_row flex_center">
          <img class="login_logo" src="../assets/imgs/logo.png" alt="logo" />
          <p class="project_name">机器人学习平台</p>
        </h3>
        <el-form-item prop="account">
          <el-input
            name="account"
            v-model="ruleForm.account"
            auto-complete="off"
            placeholder="请输入账号"
            @keyup.enter="submitForm(ruleFormRef)"
          ></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input
            type="password"
            name="password"
            v-model="ruleForm.password"
            auto-complete="off"
            placeholder="请输入密码"
            @keyup.enter="submitForm(ruleFormRef)"
          ></el-input>
        </el-form-item>
        <el-checkbox
          v-model="ruleForm.checked"
          checked
          style="margin: 0px 0px 35px 0px; float: left; color: #fff"
        >
          记住账号
        </el-checkbox>
        <el-form-item class="mt10" style="width: 100%">
          <el-button
            type="primary"
            style="width: 100%"
            @click="submitForm(ruleFormRef)"
            :disabled="ruleForm.loading"
            :loading="ruleForm.loading"
            >登录</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div id="login_bg"></div>
  </div>
</template>

<script setup lang="ts" name="Login">
import { reactive, onMounted, ref } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { useRouter } from 'vue-router'
import User from '../api/User'
import { sysDomainCode } from '@/globalSettings'
import utils from '@/assets/js/utils'
let router = useRouter()

const ruleFormRef = ref<FormInstance>()
const ruleForm = reactive({
  account: '',
  password: '',
  checked: true,
  loading: false
})
const rules = reactive<FormRules>({
  account: [
    { required: true, message: '请输入账号', trigger: 'blur' },
    { min: 3, max: 18, message: '账号长度在3到18个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 18, message: '密码长度在6到18个字符', trigger: 'blur' }
  ]
})
// 登录表单提交
const submitForm = (formName: FormInstance | undefined) => {
  if (!formName) return
  formName.validate((valid) => {
    if (valid) {
      console.log('submit!')
      ruleForm.loading = true
      const params = {
        domainCode: sysDomainCode,
        username: ruleForm.account,
        password: ruleForm.password
      }
      User.login(params)
        .then((data: any) => {
          const { code, result, msg } = data
          if (code === 0) {
            User.saveInfo(JSON.stringify(result.user)) // 保存用户信息
            utils.setCookie('UserLoginToken', result.token, '720') // 24小时x30天 = 720小时
            if (ruleForm.checked) {
              utils.setCookie('UserLoginName', ruleForm.account, '720') // 记住用户名24小时x30天 = 720小时
            } else {
              utils.deleteCookie('UserLoginName') // 删除用户名
            }
            router.push({ path: '/home' })
          } else {
            ElMessage({
              message: msg,
              type: 'error'
            })
            ruleForm.loading = false
          }
        })
        .catch((error: any) => {
          ruleForm.loading = false
          // this.CodeImg()
          ElMessage(error)
        })
    } else {
      console.log('error submit!')
      return false
    }
  })
}
// 页面加载后执行
onMounted(() => {
  const _name = utils.getCookie('UserLoginName')
  if (_name) {
    ruleForm.account = _name
  }
})
</script>

<style lang="scss" scoped>
.loginBox {
  z-index: 99;
  /*padding: 10px 25px;*/
  border-radius: 5px;
  width: 440px;
  height: 440px;
  position: absolute;
  top: 50%;
  right: 15%;
  margin-top: -220px;
  border-radius: 15px;
  background-color: rgba(42, 48, 78, 0.5);
}
.card-box {
  width: 336px;
  margin: 0 auto;
  padding-top: 50px;
}

.title {
  height: 73px;
  margin: 0px auto 30px;
  text-align: center;
  .login_logo {
    display: block;
    width: 58px;
    height: auto;
  }
  .project_name {
    font-size: 28px;
    color: #fff;
    margin-left: 8px;
    min-width: 120px;
    font-family: Arial, sans-serif; /* 使用 Arial 字体 */
    font-weight: bold; /* 字体加粗 */
    text-transform: uppercase; /* 转换为大写字母 */
    letter-spacing: 2px; /* 字间距 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3); /* 文字阴影 */
  }
}
#h-loginBox .h-loginForm {
  position: relative;
  z-index: 999;
}

#login_bg {
  width: 100%;
  height: 100%;
  position: fixed;
  background: #000000 url('../assets/imgs/login_bg.jpg') center center no-repeat;
  background-size: cover;
  z-index: 0;
  left: 0;
  top: 0;
}
</style>
