# 预警消息页面使用指南

## 页面概述

预警消息页面是预警管理模块的重要组成部分，用于查看、处理和管理系统产生的预警消息。该页面提供了丰富的搜索条件、详细的信息展示和完善的处理流程。

## 功能特性

### 1. 多条件搜索
- **客户信息**: 支持按客户名称或编号搜索
- **时间范围**: 支持按上报时间范围筛选
- **处理状态**: 支持按处理状态筛选（待处理、处理中、已处理、已忽略）
- **业务分类**: 支持按第三方名称、业务类型、预警类型筛选
- **预警级别**: 支持按预警级别筛选（高、中、低）
- **业务限定**: 支持按人员姓名或编号搜索

### 2. 数据展示
- **表格展示**: 清晰的表格布局，展示关键信息
- **状态标识**: 使用颜色标签区分预警级别和处理状态
- **分页功能**: 支持分页浏览，可调整每页显示数量
- **数据导出**: 支持将搜索结果导出为Excel文件

### 3. 消息处理
- **查看详情**: 查看预警消息的详细信息
- **立即处理**: 对待处理的预警消息进行处理
- **忽略处理**: 对不需要处理的预警消息进行忽略

## 页面结构

### 搜索区域
```
第一行：客户 | 上报时间 | 处理状态 | [查询] [重置]
第二行：第三方名称 | 业务类型 | 预警类型 | 预警级别 | 预警规则 | 限定业务
```

### 操作区域
```
左侧：[导出] 按钮
右侧：[刷新] 按钮
```

### 表格区域
| 列名 | 说明 |
|------|------|
| 预警名称 | 预警规则的名称 |
| 预警编号 | 系统生成的唯一编号 |
| 预警类型 | 预警的分类类型 |
| 第三方名称 | 相关的第三方系统名称 |
| 业务类型 | 业务分类 |
| 预警级别 | 高/中/低，用不同颜色标签显示 |
| 预警规则 | 触发预警的具体规则 |
| 直接名称 | 直接相关的名称信息 |
| 上报时间 | 预警产生的时间 |
| 操作 | 查看、处理、忽略等操作按钮 |

## 使用流程

### 1. 查询预警消息
1. 在搜索区域输入查询条件
2. 点击"查询"按钮执行搜索
3. 查看搜索结果列表

### 2. 查看消息详情
1. 点击表格中的"查看"按钮
2. 在弹出的详情对话框中查看完整信息
3. 可以在详情页面直接进行处理操作

### 3. 处理预警消息
1. 对于状态为"待处理"的消息，点击"处理"按钮
2. 在处理对话框中选择处理方式：
   - **立即处理**: 自己处理该预警
   - **转交他人**: 将预警转交给其他人员
   - **忽略处理**: 忽略该预警
3. 填写处理说明和相关信息
4. 提交处理结果

### 4. 导出数据
1. 设置需要导出的搜索条件
2. 点击"导出"按钮
3. 系统将生成Excel文件供下载

## 技术实现

### 文件结构
```
src/pages/EarlyWarnManagement/EarlyWarningMessage/
├── EarlyWarningMessage.vue          # 主页面组件
└── components/
    ├── ViewDialog.vue               # 查看详情对话框
    └── ProcessDialog.vue            # 处理对话框
```

### API接口
- `getAlertMessage`: 查询预警消息列表
- `exportAlertMessage`: 导出预警消息
- `processAlertMessage`: 处理预警消息
- `ignoreAlertMessage`: 忽略预警消息
- `searchUsers`: 搜索用户（用于转交功能）

### 路由配置
```typescript
{
  path: '/EarlyWarningMessage',
  name: 'EarlyWarningMessage',
  component: () => import('@/pages/EarlyWarnManagement/EarlyWarningMessage/EarlyWarningMessage.vue'),
  meta: { title: '预警消息', keepTags: true }
}
```

## 数据结构

### 预警消息对象
```typescript
interface AlertMessage {
  id: number                    // 消息ID
  alertName: string            // 预警名称
  alertCode: string            // 预警编号
  customerName: string         // 客户名称
  customerCode: string         // 客户编号
  thirdPartyName: string       // 第三方名称
  businessType: string         // 业务类型
  alertType: string            // 预警类型
  alertLevel: string           // 预警级别 (high/medium/low)
  alertRule: string            // 预警规则
  directName: string           // 直接名称
  limitedBusiness: string      // 限定业务
  processStatus: string        // 处理状态 (pending/processing/processed/ignored)
  reportTime: string           // 上报时间
  processTime?: string         // 处理时间
  processor?: string           // 处理人员
  createTime: string           // 创建时间
  alertContent?: string        // 预警详细内容
  processRecords?: ProcessRecord[]  // 处理记录
  attachments?: Attachment[]   // 相关附件
}
```

### 处理记录对象
```typescript
interface ProcessRecord {
  processor: string            // 处理人员
  action: string              // 处理动作 (process/ignore/transfer/close)
  processTime: string         // 处理时间
  remark?: string             // 处理备注
}
```

### 附件对象
```typescript
interface Attachment {
  fileName: string            // 文件名
  fileUrl: string            // 文件URL
  fileSize: number           // 文件大小
  uploadTime: string         // 上传时间
}
```

## 状态说明

### 处理状态
- **pending**: 待处理 - 新产生的预警消息，需要人工处理
- **processing**: 处理中 - 已分配处理人员，正在处理中
- **processed**: 已处理 - 处理完成
- **ignored**: 已忽略 - 经评估后忽略处理

### 预警级别
- **high**: 高级别 - 红色标签，需要紧急处理
- **medium**: 中级别 - 橙色标签，需要及时处理
- **low**: 低级别 - 蓝色标签，可以延后处理

## 权限控制

页面支持基于角色的权限控制：
- **查看权限**: 所有用户都可以查看预警消息列表
- **处理权限**: 只有具有处理权限的用户才能处理预警消息
- **导出权限**: 只有具有导出权限的用户才能导出数据
- **管理权限**: 管理员可以查看所有处理记录和统计信息

## 注意事项

1. **数据刷新**: 页面会定期自动刷新数据，确保信息的实时性
2. **处理时效**: 高级别预警需要在规定时间内处理完成
3. **处理记录**: 所有处理操作都会记录在案，便于追溯
4. **文件上传**: 处理过程中可以上传相关附件作为证据
5. **转交流程**: 转交的预警消息会通知相关人员

## 常见问题

### Q: 为什么看不到某些预警消息？
A: 可能是权限限制或搜索条件过于严格，请检查搜索条件或联系管理员。

### Q: 如何批量处理预警消息？
A: 目前系统支持单条处理，批量处理功能在后续版本中会添加。

### Q: 处理后的消息还能修改吗？
A: 已处理的消息不能直接修改，但可以添加补充说明。

### Q: 导出的数据包含哪些信息？
A: 导出文件包含当前搜索结果的所有字段信息，以及处理记录。

## 更新日志

- **v1.0.0**: 初始版本，包含基本的查询、查看、处理功能
- **v1.1.0**: 添加导出功能和处理记录
- **v1.2.0**: 优化用户界面和交互体验
