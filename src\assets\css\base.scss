@charset "utf-8";
body,
ul,
li,
dl,
dd,
dt,
p {
  padding: 0;
  margin: 0;
}
html,
body {
  background-color: #f6f8f9;
}
a {
  text-decoration: none;
  color: #0472e3;
}
li {
  list-style-type: none;
}
.clearfix::after {
  content: '';
  display: block;
  clear: both;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.fix {
  position: fixed;
}
.rel {
  position: relative;
}
.abs {
  position: absolute;
}
.tl {
  text-align: left;
}
.tr {
  text-align: right;
}
.tc {
  text-align: center;
}
/*Flex 布局*/
.flex_row {
  display: flex;
  flex-direction: row; // 起点在左端
}
.flex_row_right {
  display: flex;
  flex-direction: row-reverse; // 起点在右端
}
.flex_column {
  display: flex;
  flex-direction: column;
}
.flex_row_center {
  justify-content: center;
}
.flex_column_center {
  align-items: center;
}
.flex_center {
  justify-content: center;
  align-items: center;
}
.self_flex_start {
  align-self: flex-start;
}
.self_flex_end {
  align-self: flex-end;
}
/*Flex 布局*/
/********** 自定义间距 ********************/
.ml5 {
  margin-left: 5px;
}
.ml10 {
  margin-left: 10px;
}
.ml20 {
  margin-left: 20px;
}
.ml100 {
  margin-left: 100px;
}
.mr10 {
  margin-right: 10px;
}
.mb10 {
  margin-bottom: 10px;
}
.mt7 {
  margin-top: 7px;
}
.mt10 {
  margin-top: 10px;
}
.mt20 {
  margin-top: 20px;
}
.mt25 {
  margin-top: 25px;
}
.mt30 {
  margin-top: 30px;
}
.mt40 {
  margin-top: 40px;
}
.pl10 {
  padding-left: 10px;
}
.pl15 {
  padding-left: 15px;
}
.pl20 {
  padding-left: 20px;
}
.pr10 {
  padding-right: 10px;
}
.pr20 {
  padding-right: 20px;
}
.pt10 {
  padding-top: 10px;
}
.pt15 {
  padding-top: 15px;
}
.pt20 {
  padding-top: 20px;
}
.pt25 {
  padding-top: 25px;
}
.pt30 {
  padding-top: 30px;
}
.pt40 {
  padding-top: 40px;
}
.pt50 {
  padding-top: 50px;
}
.pb10 {
  padding-bottom: 10px;
}
.pb15 {
  padding-bottom: 15px;
}
.pb20 {
  padding-bottom: 20px;
}
.pb25 {
  padding-bottom: 25px;
}
.pb30 {
  padding-bottom: 30px;
}
.pb40 {
  padding-bottom: 40px;
}
.pb50 {
  padding-bottom: 50px;
}
/********** 自定义间距 ********************/

/********** 行高 ********************/
.lh26 {
  line-height: 26px;
}
.lh28 {
  line-height: 28px;
}
/********** 行高 ********************/

.dashed_top {
  border-top: 1px dashed #e0e0e0;
}
.border_top {
  border-top: 1px solid #e0e0e0;
}

/*常用字体设置*/
.fc-danger {
  color: #f56c6c;
}
.fc-success {
  color: #67c23a;
}
.fc-warning {
  color: #e6a23c;
}
.fc-info {
  color: #909399;
}
.fc-primary {
  color: #409eff;
}

.f-bold {
  font-weight: bold;
}

.fs12 {
  font-size: 12px;
}
.fs14 {
  font-size: 14px;
}
.fs16 {
  font-size: 16px;
}

textarea {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', Arial, sans-serif;
}
textarea::placeholder {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei',
    '微软雅黑', Arial, sans-serif;
}
