# 预警后台管理系统

## 项目概览

预警后台管理系统是一个基于Vue3 + TypeScript + Element Plus的现代化Web应用，主要用于管理预警模型、预警事件和数据源等核心业务功能。

### 核心特性

- 🚀 **现代化技术栈**: Vue3 + TypeScript + Vite + Pinia
- 🎨 **优雅UI设计**: Element Plus组件库，支持多主题切换
- 🌍 **国际化支持**: Vue-i18n多语言切换
- 📱 **响应式布局**: 支持多种布局模式（垂直、水平、列式）
- 🔐 **权限管理**: 基于指令的权限控制
- 📊 **数据可视化**: 集成图表展示功能
- 🔄 **状态管理**: Pinia状态管理，支持持久化
- 🛠 **开发体验**: ESLint + Prettier代码规范，热重载开发

### 主要功能模块

#### 1. 数据源管理
- **API数据源**: 管理外部API数据源配置
- 支持数据源的增删改查操作
- 数据源连接测试和验证

#### 2. 预警管理
- **预警模型**: 创建和管理预警规则模型
- **预警事件**: 查看和处理预警事件
- **预警消息**: 预警消息的查看、处理和管理
- 支持多种业务类型和预警类型
- 预警事件和消息导出功能

#### 3. 系统管理
- **数据字典**: 系统配置项管理
- **用户权限**: 基于角色的权限控制
- **系统设置**: 主题、语言等个性化配置

## 快速开始

### 环境要求

- Node.js >= 16.0.0
- npm >= 8.0.0 或 yarn >= 1.22.0

### 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 开发环境启动

```bash
# 开发环境
npm run dev

# 测试环境
npm run test
```

访问 http://localhost:5173 查看应用

### 构建部署

```bash
# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:pro
```

### 代码检查和格式化

```bash
# ESLint检查
npm run lint

# Prettier格式化
npm run format

# TypeScript类型检查
npm run type-check
```

## 项目结构

```
warning-system-pc/
├── docs/                    # 项目文档
├── public/                  # 静态资源
├── src/
│   ├── api/                # API接口定义
│   ├── assets/             # 静态资源
│   │   ├── css/           # 样式文件
│   │   ├── font/          # 字体文件
│   │   ├── imgs/          # 图片资源
│   │   └── js/            # 工具函数
│   ├── components/         # 公共组件
│   │   ├── home/          # 首页组件
│   │   └── layoutBox/     # 布局组件
│   ├── config/            # 配置文件
│   ├── directives/        # 自定义指令
│   ├── i18n/              # 国际化配置
│   ├── pages/             # 页面组件
│   │   ├── DataSouceManagement/    # 数据源管理
│   │   ├── EarlyWarnManagement/    # 预警管理
│   │   └── SystemManagement/       # 系统管理
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   ├── App.vue            # 根组件
│   ├── main.ts            # 应用入口
│   └── globalSettings.ts  # 全局配置
├── package.json           # 项目配置
├── vite.config.ts         # Vite配置
└── tsconfig.json          # TypeScript配置
```

## 技术栈

### 核心框架
- **Vue 3.2.47**: 渐进式JavaScript框架
- **TypeScript 4.8.4**: JavaScript的超集，提供类型安全
- **Vite 4.1.4**: 下一代前端构建工具

### UI组件库
- **Element Plus 2.9.5**: Vue3组件库
- **@better-scroll/core**: 移动端滚动解决方案

### 状态管理与路由
- **Pinia 2.0.32**: Vue3官方推荐状态管理库
- **Vue Router 4.1.6**: Vue3官方路由管理器

### 工具库
- **Axios 1.3.4**: HTTP客户端
- **Vue-i18n 9.2.2**: 国际化解决方案
- **Screenfull 6.0.2**: 全屏API封装

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Sass**: CSS预处理器

## 环境配置

项目支持多环境配置：

- **开发环境** (dev): 本地开发使用
- **测试环境** (test): 测试服务器部署
- **生产环境** (pro): 正式服务器部署

### 代理配置

开发环境配置了API代理：

```typescript
proxy: {
  '^/sso-service': {
    target: 'http://sso.qmwallet.vip',
    changeOrigin: true
  },
  '^/alert-converge-api': {
    target: 'https://alert.qmwallet.vip',
    changeOrigin: true
  }
}
```

## 常见问题

### 1. 启动时端口被占用
```bash
# 指定端口启动
npm run dev -- --port 3000
```

### 2. 依赖安装失败
```bash
# 清除缓存重新安装
npm cache clean --force
rm -rf node_modules package-lock.json
npm install
```

### 3. TypeScript类型错误
```bash
# 运行类型检查
npm run type-check
```

### 4. 样式不生效
检查是否正确导入了全局样式文件和主题配置。

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

如有问题或建议，请联系开发团队。
