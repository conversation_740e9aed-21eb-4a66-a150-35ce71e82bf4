<template>
  <div class="box">
    <!-- <p class="title">{{ t('home.welcome') }}</p> -->
    <p class="title">国际化获取文本:{{ translateLang('欢迎登录机器人学习平台') }}</p>
    <p class="title">
      国际化变量传输:{{
        translateLang('本次共成功撤销{successCount}条订单，{failCount}条订单撤销失败', {
          successCount: 1000,
          failCount: 20
        })
      }}
    </p>
    <p class="title">国际化获取对象:{{ translateConfigObjLang('aaa.b') }}</p>
    <el-select v-model="state.selectValue">
      <el-option
        v-for="item in translateConfigObjLang('aaa.b')"
        :key="item.value"
        :value="item.value"
        :label="item.name"
      />
    </el-select>
    <p>{{ state.selectValue }}</p>
    <el-config-provider>
      <el-table mb-1 :data="[]" />
      <el-pagination :total="100" />
    </el-config-provider>

    <el-button type="primary" @click="open">显示</el-button>

    <!-- <el-row class="mb-4">
      <el-button>Default</el-button>
      <el-button type="primary">Primary</el-button>
      <el-button type="success">Success</el-button>
      <el-button type="info">Info</el-button>
      <el-button type="warning">Warning</el-button>
      <el-button type="danger">Danger</el-button>
    </el-row>

    <el-row class="mb-4">
      <el-button plain>Plain</el-button>
      <el-button type="primary" plain>Primary</el-button>
      <el-button type="success" plain>Success</el-button>
      <el-button type="info" plain>Info</el-button>
      <el-button type="warning" plain>Warning</el-button>
      <el-button type="danger" plain>Danger</el-button>
    </el-row>

    <el-row class="mb-4">
      <el-button round>Round</el-button>
      <el-button type="primary" round>Primary</el-button>
      <el-button type="success" round>Success</el-button>
      <el-button type="info" round>Info</el-button>
      <el-button type="warning" round>Warning</el-button>
      <el-button type="danger" round>Danger</el-button>
    </el-row> -->
  </div>
</template>

<script setup lang="ts">
// import { useI18n } from 'vue-i18n'
// const { t } = useI18n()
import { reactive } from 'vue'
import { translateLang, translateConfigObjLang } from '@/i18n'
// 状态管理
const state = reactive({
  selectValue: ''
})

// 方法
const open = () => {
  console.log('open')
  const text = translateLang('欢迎登录机器人学习平台')
  console.log(text)
  // ElMessageBox.alert('This is a message', 'Title', {
  //   // if you want to disable its autofocus
  //   // autofocus: false,
  //   confirmButtonText: 'OK',
  //   callback: (action: Action) => {
  //     ElMessage({
  //       type: 'info',
  //       message: `action: ${action}`,
  //     })
  //   },
  // })
}
</script>

<style lang="scss" scoped>
.box {
  height: 1500px;
  background-color: #fff;
  padding: 10px;
  .title {
    font-size: 16px;
  }
}
</style>