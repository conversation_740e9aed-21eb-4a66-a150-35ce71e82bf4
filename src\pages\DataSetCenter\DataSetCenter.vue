<!--数据集中心-->
<template>
  <div>
    <div>
      <p>1、权限指令</p>
      <button v-has-permission="['查看']">查看</button>
      <button v-has-permission="['删除']">删除</button>
    </div>
    <div>
      <p>2、路由tab切换，不新增tab配置</p>
      <el-tabs type="border-card" v-model="activeName" @tab-click="handleClick">
        <el-tab-pane label="User" name="first">
          <div>User</div>
        </el-tab-pane>
        <el-tab-pane label="Config" name="second"><div>Config</div></el-tab-pane>
        <el-tab-pane label="Role" name="third"><div>Role</div></el-tab-pane>
        <el-tab-pane label="Task" name="fourth"><div>Task</div></el-tab-pane>
      </el-tabs>
    </div>
    <div class="content">
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
      <p>数据集中心</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import type { TabsPaneContext } from 'element-plus'
const router = useRouter()
const routers = useRoute()

const activeName = ref('')

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab.props.name, event)
  router.push({ path: '/DataSetCenter', query: { id: routers.query.id, tab: tab.props.name } })
}

onMounted(() => {
  console.log('数据集中心')
  activeName.value = routers.query.tab || 'first'
})
</script>

<style lang="scss" scoped>
.content {
  p {
    font-size: 150px;
    margin-top: 100px;
  }
}
</style>
