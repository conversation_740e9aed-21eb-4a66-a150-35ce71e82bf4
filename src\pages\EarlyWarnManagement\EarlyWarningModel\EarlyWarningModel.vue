<!--预警模型-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="预警模型名称：" prop="modelName">
        <el-input v-model="search.modelName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="预警模型编号：" prop="modelCode">
        <el-input v-model="search.modelCode" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select v-model="search.businessType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
<!--      <el-form-item label="预警级别：" prop="accountId">-->
<!--        <el-select v-model="search.devDept" placeholder="请选择" :empty-values="[null, undefined]">-->
<!--          <el-option label="全部" value="" />-->
<!--          <el-option-->
<!--            v-for="item in (store() as any).businessTypeOptions"-->
<!--            :key="item.id"-->
<!--            :label="item.cloudProviderName"-->
<!--            :value="item.id" />-->
<!--        </el-select>-->
<!--      </el-form-item>-->
      <el-form-item label="预警类型：" prop="alertType">
        <el-select v-model="search.alertType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in (store() as any).alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    <el-button class="mb10" type="primary" @click="editDialog({}, 1)">添加</el-button>
    <el-table
      :data="tableData"
      border
      stripe
      v-loading="loading"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="modelName" label="模型名称" />
      <el-table-column prop="modelCode" label="模型编号" />
      <el-table-column prop="businessType" label="业务类型">
        <template #default="{ row }">
          {{ optionDeptVal(row.businessType, (store() as any).businessTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column prop="alertType" label="预警类型">
        <template #default="{ row }">
          {{ optionDeptVal(row.alertType, (store() as any).alertTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column label="是否关联规则">
        <template #default="{ row }">
          {{ row.related ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column prop="description" label="模型说明" />
      <el-table-column label="操作" width="160">
        <template #default="{ row }">
          <el-button type="primary" text @click="editDialog(row, 2)">编辑</el-button>
          <el-button type="danger" text @click="deleteData(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <AddEditDialog
      v-model:dialogVisible="warnEditDialog.show"
      :dialogForm="warnEditDialog.form"
      :dialogType="warnEditDialog.type"
      @callback="getList"
    ></AddEditDialog>
  </div>
</template>

<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance } from 'element-plus'
import AddEditDialog from './components/AddEditDialog.vue'
import WarnApi from '@/api/EarlyWarnManage'
import { store } from '@/stores'

// 搜索数据
const search = reactive({
  modelName: '', // 预警模型名称
  modelCode: '', // 预警模型编号
  businessType: '', // 业务类型
  alertType: '' // 预警类型
})

// 表格
let tableData = reactive([])
const loading = ref(false)

let pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const optionDeptVal = (val: any, obj: any[], propName = 'label', id = 'value') => {
  const item = obj.find(item => item[id] === val)
  return item ? item[propName] : val
}

// 获取数据
const getList = (page = 1) => {
  let params = {
    pageNum: page || 1,
    pageSize: pagination.pageSize,
    modelName: search.modelName,
    modelCode: search.modelCode,
    businessType: search.businessType,
    alertType: search.alertType
  }
  loading.value = true
  WarnApi.getAlertModel(params).then((res: any) => {
    loading.value = false
    const { code, data, message } = res || {}
    if (code === '0000') {
      tableData = data.records
      pagination.total = Number(data.total)
      pagination.currentPage = Number(data.current)
    } else {
      ElMessage({ message: message, type: 'error' })
    }
  })
}

const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}
const handleCurrentChange = (val: number) => {
  pagination.currentPage = val
  getList(val)
}

// 删除
const deleteData = (id: string) => {
  ElMessageBox.confirm('确定要删除该模型吗？', {
    title: '删除提示',
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    WarnApi.delAlertModel({id}).then((res: any) => {
      if (res.code === '0000') {
        getList(1)
        ElMessage({ message: '删除成功', type: 'success' })
      } else {
        ElMessage({ message: res.message, type: 'error' })
      }
    })
  })
}

// 清除过滤条件
const searchFormRef = ref<FormInstance>()
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

const warnEditDialog = reactive({
  show: false,
  form: {},
  type: 1
})

// 添加编辑弹窗
const editDialog = (row: any, type: number) => {
  warnEditDialog.show = true
  warnEditDialog.type = type
  warnEditDialog.form = row
}

onMounted(async () => {
  await store().allBusinessType()
  await store().allAlertType()
  getList(1)
})
</script>

<style lang="scss" scoped>
.el-input {
  width: 220px;
}
.el-select {
  width: 220px;
}
.el-message-box__container {
  align-items: normal;
}
</style>
