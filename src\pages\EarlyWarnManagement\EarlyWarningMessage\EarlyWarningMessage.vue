<!--预警消息-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="客户：" prop="customerName">
        <el-input v-model="search.customerName" placeholder="请输入客户名称或编号" />
      </el-form-item>
      <el-form-item label="上报时间：" prop="reportTimeRange">
        <el-date-picker
          v-model="search.reportTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
      <el-form-item label="处理状态：" prop="processStatus">
        <el-select v-model="search.processStatus" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option label="待处理" value="pending" />
          <el-option label="处理中" value="processing" />
          <el-option label="已处理" value="processed" />
          <el-option label="已忽略" value="ignored" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
      </el-form-item>
    </el-form>
    
    <el-form :inline="true" :model="search" class="mt10">
      <el-form-item label="第三方名称：" prop="thirdPartyName">
        <el-select v-model="search.thirdPartyName" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option label="国际业务" value="international" />
          <el-option label="国内业务" value="domestic" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select v-model="search.businessType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in mainStore.businessTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警类型：" prop="alertType">
        <el-select v-model="search.alertType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option
            v-for="item in mainStore.alertTypeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警级别：" prop="alertLevel">
        <el-select v-model="search.alertLevel" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警规则：" prop="alertRule">
        <el-select v-model="search.alertRule" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option label="是" value="yes" />
          <el-option label="否" value="no" />
        </el-select>
      </el-form-item>
      <el-form-item label="限定业务：" prop="limitedBusiness">
        <el-input v-model="search.limitedBusiness" placeholder="请输入人员姓名或编号" />
      </el-form-item>
    </el-form>

    <div class="mb10 flex_row" style="justify-content: space-between;">
      <div>
        <el-button type="primary" @click="handleExport">导出</el-button>
      </div>
      <div>
        <el-button @click="getList(pagination.currentPage)">
          <el-icon><Refresh /></el-icon>
        </el-button>
      </div>
    </div>

    <el-table
      :data="tableData"
      border
      stripe
      v-loading="loading"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column prop="alertName" label="预警名称" min-width="120" />
      <el-table-column prop="alertCode" label="预警编号" min-width="120" />
      <el-table-column prop="alertType" label="预警类型" min-width="100">
        <template #default="{ row }">
          {{ optionDeptVal(row.alertType, mainStore.alertTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column prop="thirdPartyName" label="第三方名称" min-width="120">
        <template #default="{ row }">
          {{ getThirdPartyName(row.thirdPartyName) }}
        </template>
      </el-table-column>
      <el-table-column prop="businessType" label="业务类型" min-width="100">
        <template #default="{ row }">
          {{ optionDeptVal(row.businessType, mainStore.businessTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column prop="alertLevel" label="预警级别" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getAlertLevelType(row.alertLevel)">
            {{ getAlertLevelText(row.alertLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="alertRule" label="预警规则" min-width="120" />
      <el-table-column prop="directName" label="直接名称" min-width="120" />
      <el-table-column prop="reportTime" label="上报时间" min-width="160" />
      <el-table-column label="操作" width="160" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" text @click="handleView(row)">查看</el-button>
          <el-button type="success" text @click="handleProcess(row)" v-if="row.processStatus === 'pending'">
            处理
          </el-button>
          <el-button type="info" text @click="handleIgnore(row)" v-if="row.processStatus === 'pending'">
            忽略
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看详情对话框 -->
    <!-- <ViewDialog
      v-model:dialogVisible="viewDialog.show"
      :dialogForm="viewDialog.form"
      @callback="getList"
    /> -->

    <!-- 处理对话框 -->
    <!-- <ProcessDialog
      v-model:dialogVisible="processDialog.show"
      :dialogForm="processDialog.form"
      @callback="getList"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Refresh } from '@element-plus/icons-vue'
import { store } from '@/stores'
import type { FormInstance } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'
import { optionDeptVal } from '@/assets/js/filters'
// import ViewDialog from './components/ViewDialog.vue'
// import ProcessDialog from './components/ProcessDialog.vue'

// 搜索表单
const search = reactive({
  customerName: '',
  reportTimeRange: [],
  processStatus: '',
  thirdPartyName: '',
  businessType: '',
  alertType: '',
  alertLevel: '',
  alertRule: '',
  limitedBusiness: ''
})

// 表格数据
const tableData = ref([])
const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 对话框
const viewDialog = reactive({
  show: false,
  form: {}
})

const processDialog = reactive({
  show: false,
  form: {}
})

const searchFormRef = ref<FormInstance>()
const mainStore = store()

// 获取列表数据
const getList = async (page = 1) => {
  try {
    loading.value = true
    pagination.currentPage = page
    
    const params = {
      page: pagination.currentPage,
      size: pagination.pageSize,
      ...search,
      startTime: search.reportTimeRange?.[0] || '',
      endTime: search.reportTimeRange?.[1] || ''
    }
    
    const res = await WarnApi.getAlertMessage(params)
    if (res.code === '0000') {
      tableData.value = res.data.records
      pagination.total = res.data.total
    }
  } catch (error) {
    ElMessage.error('获取数据失败')
  } finally {
    loading.value = false
  }
}

// 重置表单
const resetForm = (formEl: any) => {
  if (!formEl) return
  formEl.resetFields()
  Object.assign(search, {
    customerName: '',
    reportTimeRange: [],
    processStatus: '',
    thirdPartyName: '',
    businessType: '',
    alertType: '',
    alertLevel: '',
    alertRule: '',
    limitedBusiness: ''
  })
  getList(1)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}

const handleCurrentChange = (val: number) => {
  getList(val)
}

// 获取第三方名称
const getThirdPartyName = (value: string) => {
  const map: Record<string, string> = {
    international: '国际业务',
    domestic: '国内业务'
  }
  return map[value] || value
}

// 获取预警级别类型
const getAlertLevelType = (level: string) => {
  const typeMap: Record<string, 'danger' | 'warning' | 'info' | 'success' | 'primary'> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[level] || 'info'
}

// 获取预警级别文本
const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || level
}

// 查看详情
const handleView = (row: any) => {
  viewDialog.form = { ...row }
  viewDialog.show = true
}

// 处理预警
const handleProcess = (row: any) => {
  processDialog.form = { ...row }
  processDialog.show = true
}

// 忽略预警
const handleIgnore = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要忽略这条预警消息吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await WarnApi.ignoreAlertMessage({ id: row.id })
    ElMessage.success('操作成功')
    getList(pagination.currentPage)
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
    }
  }
}

// 导出数据
const handleExport = async () => {
  try {
    const params = {
      ...search,
      startTime: search.reportTimeRange?.[0] || '',
      endTime: search.reportTimeRange?.[1] || ''
    }
    
    await WarnApi.exportAlertMessage(params)
    ElMessage.success('导出成功')
  } catch (error) {
    ElMessage.error('导出失败')
  }
}

onMounted(() => {
  // 获取字典数据
  mainStore.allBusinessType()
  mainStore.allAlertType()
  // 获取列表数据
  getList()
})
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.mt20 {
  margin-top: 20px;
}

.flex_row {
  display: flex;
  flex-direction: row;
}

.flex_center {
  justify-content: center;
  align-items: center;
}
</style>
