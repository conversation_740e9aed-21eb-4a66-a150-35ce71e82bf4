<!--预警消息-->
<template>
  <div>
    <el-form :inline="true" :model="search" ref="searchFormRef">
      <el-form-item label="客户：" prop="customerName">
        <el-input v-model="search.customerName" placeholder="请输入人员姓名或编号" />
      </el-form-item>
      <el-form-item label="上报时间：" prop="reportTimeRange">
        <el-date-picker
          v-model="search.reportTimeRange"
          type="datetimerange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="getList(1)">查询</el-button>
        <el-button @click="resetForm(searchFormRef)">重置</el-button>
        <el-button @click="handleExport">下载</el-button>
      </el-form-item>
    </el-form>

    <el-form :inline="true" :model="search" class="mt10">
      <el-form-item label="第三方名称：" prop="thirdPartyName">
        <el-select v-model="search.thirdPartyName" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="国际业务" value="international" />
          <el-option label="国内业务" value="domestic" />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型：" prop="businessType">
        <el-select v-model="search.businessType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="外汇" value="forex" />
          <el-option label="安全类" value="security" />
          <el-option label="业务类" value="business" />
          <el-option label="金融类" value="finance" />
          <el-option label="运营类" value="operation" />
        </el-select>
      </el-form-item>
      <el-form-item label="系统名称：" prop="systemName">
        <el-select v-model="search.systemName" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="中台" value="middle" />
          <el-option label="前台" value="frontend" />
          <el-option label="后台" value="backend" />
          <el-option label="核心系统" value="core" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警类型：" prop="alertType">
        <el-select v-model="search.alertType" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="全部" value="" />
          <el-option label="警告" value="warning" />
          <el-option label="错误" value="error" />
          <el-option label="信息" value="info" />
          <el-option label="严重" value="critical" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警级别：" prop="alertLevel">
        <el-select v-model="search.alertLevel" placeholder="请选择" :empty-values="[null, undefined]">
          <el-option label="高" value="high" />
          <el-option label="中" value="medium" />
          <el-option label="低" value="low" />
        </el-select>
      </el-form-item>
    </el-form>

    <el-table
      :data="tableData"
      border
      stripe
      v-loading="loading"
      :cell-style="{ textAlign: 'center' }"
      :header-cell-style="{ textAlign: 'center' }"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="alertName" label="预警名称" min-width="120" />
      <el-table-column prop="alertCode" label="预警编号" min-width="120" />
      <el-table-column prop="alertType" label="预警类型" min-width="100">
        <template #default="{ row }">
          {{ optionDeptVal(row.alertType, alertTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column prop="thirdPartyName" label="第三方名称" min-width="120">
        <template #default="{ row }">
          {{ getThirdPartyName(row.thirdPartyName) }}
        </template>
      </el-table-column>
      <el-table-column prop="businessType" label="业务类型" min-width="100">
        <template #default="{ row }">
          {{ optionDeptVal(row.businessType, businessTypeOptions) }}
        </template>
      </el-table-column>
      <el-table-column prop="alertLevel" label="预警级别" min-width="100">
        <template #default="{ row }">
          <el-tag :type="getAlertLevelType(row.alertLevel)">
            {{ getAlertLevelText(row.alertLevel) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="systemName" label="系统名称" min-width="120" />
      <el-table-column prop="custody" label="托管" min-width="200" />
      <el-table-column prop="reportTime" label="上报时间" min-width="160" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" text @click="handleView(row)">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <div class="mt20 flex_row flex_center">
      <el-pagination
        v-model:current-page="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        :page-sizes="[5, 10, 20, 50, 100]"
        :background="true"
        layout="prev, pager, next, jumper, sizes, total"
        :total="pagination.total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 查看详情对话框 -->
    <!-- <ViewDialog
      v-model:dialogVisible="viewDialog.show"
      :dialogForm="viewDialog.form"
      @callback="getList"
    /> -->

    <!-- 处理对话框 -->
    <!-- <ProcessDialog
      v-model:dialogVisible="processDialog.show"
      :dialogForm="processDialog.form"
      @callback="getList"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

import type { FormInstance } from 'element-plus'
import { optionDeptVal } from '@/assets/js/filters'

// 搜索表单
const search = reactive({
  customerName: '',
  reportTimeRange: [],
  thirdPartyName: 'international',
  businessType: 'forex',
  systemName: 'middle',
  alertType: '',
  alertLevel: 'high'
})

// 静态选项数据
const businessTypeOptions = ref([
  { value: 'forex', label: '外汇' },
  { value: 'security', label: '安全类' },
  { value: 'business', label: '业务类' },
  { value: 'finance', label: '金融类' },
  { value: 'operation', label: '运营类' }
])

const alertTypeOptions = ref([
  { value: 'warning', label: '警告' },
  { value: 'error', label: '错误' },
  { value: 'info', label: '信息' },
  { value: 'critical', label: '严重' }
])



// 模拟表格数据
const tableData = ref([
  {
    id: 1,
    alertName: '我的预警主要预警',
    alertCode: '10012',
    alertType: 'warning',
    thirdPartyName: 'international',
    businessType: 'forex',
    alertLevel: 'high',
    systemName: 'middle',
    custody: '我的预警主要预警',
    reportTime: '2024-11-11 8:25:36'
  },
  {
    id: 2,
    alertName: '我的预警主要预警',
    alertCode: '10012',
    alertType: 'warning',
    thirdPartyName: 'international',
    businessType: 'forex',
    alertLevel: 'high',
    systemName: 'middle',
    custody: '"我的预警": "1000"\n"小时预警数量": "300"\n"小时预警级别": "300"\n"我的预警主要": "300"',
    reportTime: '2024-11-11 8:25:36'
  }
])

const loading = ref(false)

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 2
})

const searchFormRef = ref<FormInstance>()

// 获取列表数据（静态模拟）
const getList = (page = 1) => {
  loading.value = true

  // 模拟异步加载
  setTimeout(() => {
    pagination.currentPage = page
    // 这里可以根据搜索条件过滤数据，暂时显示所有数据
    loading.value = false
    ElMessage.success('数据加载成功')
  }, 500)
}

// 重置表单
const resetForm = (formEl: any) => {
  if (!formEl) return
  formEl.resetFields()
  Object.assign(search, {
    customerName: '',
    reportTimeRange: [],
    thirdPartyName: 'international',
    businessType: 'forex',
    systemName: 'middle',
    alertType: '',
    alertLevel: 'high'
  })
  getList(1)
}

// 分页处理
const handleSizeChange = (val: number) => {
  pagination.pageSize = val
  getList(1)
}

const handleCurrentChange = (val: number) => {
  getList(val)
}

// 获取第三方名称
const getThirdPartyName = (value: string) => {
  const map: Record<string, string> = {
    international: '国际业务',
    domestic: '国内业务'
  }
  return map[value] || value
}

// 获取预警级别类型
const getAlertLevelType = (level: string) => {
  const typeMap: Record<string, 'danger' | 'warning' | 'info' | 'success' | 'primary'> = {
    high: 'danger',
    medium: 'warning',
    low: 'info'
  }
  return typeMap[level] || 'info'
}

// 获取预警级别文本
const getAlertLevelText = (level: string) => {
  const textMap: Record<string, string> = {
    high: '高',
    medium: '中',
    low: '低'
  }
  return textMap[level] || level
}

// 查看详情
const handleView = (row: any) => {
  ElMessage.info('查看详情功能待后端接口完成后实现')
  console.log('查看详情:', row)
}



// 导出数据
const handleExport = () => {
  ElMessage.info('导出功能待后端接口完成后实现')
  console.log('导出参数:', {
    ...search,
    startTime: search.reportTimeRange?.[0] || '',
    endTime: search.reportTimeRange?.[1] || ''
  })
}
</script>

<style lang="scss" scoped>
.mt10 {
  margin-top: 10px;
}

.mb10 {
  margin-bottom: 10px;
}

.mt20 {
  margin-top: 20px;
}

.flex_row {
  display: flex;
  flex-direction: row;
}

.flex_center {
  justify-content: center;
  align-items: center;
}
</style>
