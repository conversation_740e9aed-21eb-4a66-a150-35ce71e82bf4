/***
 * @params
 * title： 字符串   desc：页面标题配置
 * keepAlive: 布尔值  desc：当前页面是否加入缓存  vue3的页面的文件名name就是文件名（例如：AddOrViewDataSet.vue文件  name默认是 AddOrViewDataSet）
 * keepTags: 布尔值   desc：配置成true之后，路由从当前路由跳转至当前路由 不新建tab
 * isShow: 布尔值     desc：是否显示在菜单栏
 * ***/
import { createRouter, createWebHashHistory, type RouteMeta } from 'vue-router'
// 定义路由 元信息的 类型
declare module 'vue-router' {
  interface RouteMeta {
    title: string
    keepAlive: boolean
    keepTags: boolean
  }
}

const defaultMeta: RouteMeta = {
  title: '',
  keepAlive: false,
  keepTags: false,
  isShow: true
}
const layoutBox = () => import('@/components/layoutBox/index.vue')
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      meta: { ...defaultMeta, title: '登录' },
      component: () => import('@/pages/LoginPage.vue')
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '首页' },
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@/pages/HomePage.vue'),
          meta: { ...defaultMeta }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '数据集中心' },
      children: [
        {
          path: '/DataSetCenter',
          name: 'DataSetCenter',
          component: () => import('@/pages/DataSetCenter/DataSetCenter.vue'),
          meta: { ...defaultMeta, title: '数据集中心', keepTags: true }
        },
        {
          path: '/DataSourceConfigure',
          name: 'DataSourceConfigure',
          component: () => import('@/pages/DataSetCenter/DataSourceConfigure.vue'),
          meta: { ...defaultMeta, title: '数据源配置' }
        },
        {
          path: '/AddDataSet',
          name: 'AddDataSet',
          component: () => import('@/pages/DataSetCenter/AddDataSet.vue'),
          meta: { ...defaultMeta, title: '添加数据集' }
        },
        {
          path: '/ViewDataSet',
          name: 'ViewDataSet',
          component: () => import('@/pages/DataSetCenter/ViewDataSet.vue'),
          meta: { ...defaultMeta, title: '查看数据集详情' }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '特征筛选中心' },
      children: [
        {
          path: '/featureFilter',
          name: 'featureFilter',
          component: () => import('@/pages/featureFilterCenter/featureFilter/index.vue'),
          meta: { ...defaultMeta, title: '特征筛选' }
        },
        {
          path: '/featureView',
          name: 'featureView',
          component: () => import('@/pages/featureFilterCenter/featureView/index.vue'),
          meta: { ...defaultMeta, title: '特征查看', keepAlive: true }
        },
        {
          path: '/featureResultView',
          name: 'featureResultView',
          component: () =>
            import('@/pages/featureFilterCenter/featureView/components/featureResultView.vue'),
          meta: { ...defaultMeta, title: '特征结果查看' }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '建模中心' },
      children: [
        {
          path: '/modeling',
          name: 'modeling',
          component: () => import('@/pages/modelCenter/modeling/index.vue'),
          meta: { ...defaultMeta, title: '建模', keepAlive: true }
        },
        {
          path: '/reportCenter',
          name: 'reportCenter',
          component: () => import('@/pages/modelCenter/reportCenter/index.vue'),
          meta: { ...defaultMeta, title: '报告中心', keepAlive: true }
        },
        {
          path: '/reportChart',
          name: 'reportChart',
          component: () => import('@/pages/modelCenter/reportCenter/components/reportChart.vue'),
          meta: { ...defaultMeta, title: '报告图表', keepAlive: false }
        },
        {
          path: '/modelReview',
          name: 'modelReview',
          component: () => import('@/pages/modelCenter/modelReview/index.vue'),
          meta: { ...defaultMeta, title: '模型审核', keepAlive: true }
        },
        {
          path: '/JupyterNotebook',
          name: 'JupyterNotebook',
          component: () => import('@/pages/modelCenter/JupyterNotebook.vue'),
          meta: { ...defaultMeta, title: 'JupyterNotebook', keepAlive: false }
        }
      ]
    },
    {
      path: '/nofind',
      component: () => import('@/pages/NoFind.vue')
    },
    {
      path: '/:catchAll(.*)',
      redirect: '/nofind'
    }
  ]
})

export default router
