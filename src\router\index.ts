/***
 * @params
 * title： 字符串   desc：页面标题配置
 * keepAlive: 布尔值  desc：当前页面是否加入缓存  vue3的页面的文件名name就是文件名（例如：AddOrViewDataSet.vue文件  name默认是 AddOrViewDataSet）
 * keepTags: 布尔值   desc：配置成true之后，路由从当前路由跳转至当前路由 不新建tab
 * isShow: 布尔值     desc：是否显示在菜单栏
 * ***/
import { createRouter, createWebHashHistory, type RouteMeta } from 'vue-router'
// 定义路由 元信息的 类型
declare module 'vue-router' {
  interface RouteMeta {
    title: string
    keepAlive: boolean
    keepTags: boolean
  }
}

const defaultMeta: RouteMeta = {
  title: '',
  keepAlive: false,
  keepTags: false,
  isShow: true
}
const layoutBox = () => import('@/components/layoutBox/index.vue')
const router = createRouter({
  history: createWebHashHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login'
    },
    {
      path: '/login',
      name: 'login',
      meta: { ...defaultMeta, title: '登录' },
      component: () => import('@/pages/LoginPage.vue')
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '首页' },
      children: [
        {
          path: '/home',
          name: 'home',
          component: () => import('@/pages/HomePage.vue'),
          meta: { ...defaultMeta }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '数据源管理' },
      children: [
        {
          path: '/ApiDataSources',
          name: 'ApiDataSources',
          component: () => import('@/pages/DataSouceManagement/ApiDataSources/ApiDataSources.vue'),
          meta: { ...defaultMeta, title: 'API数据源', keepTags: true }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '预警管理' },
      children: [
        {
          path: '/EarlyWarnModel',
          name: 'EarlyWarnModel',
          component: () => import('@/pages/EarlyWarnManagement/EarlyWarningModel/EarlyWarningModel.vue'),
          meta: { ...defaultMeta, title: '预警模型', keepTags: true }
        },
        {
          path: '/EarlyWarningEvent',
          name: 'EarlyWarningEvent',
          component: () => import('@/pages/EarlyWarnManagement/EarlyWarningEvent/EarlyWarningEvent.vue'),
          meta: { ...defaultMeta, title: '预警事件', keepTags: true }
        },
        {
          path: '/EarlyWarningMessage',
          name: 'EarlyWarningMessage',
          component: () => import('@/pages/EarlyWarnManagement/EarlyWarningMessage/EarlyWarningMessage.vue'),
          meta: { ...defaultMeta, title: '预警消息', keepTags: true }
        }
      ]
    },
    {
      path: '/',
      component: layoutBox,
      meta: { ...defaultMeta, title: '系统管理' },
      children: [
        {
          path: '/DataDictionary',
          name: 'DataDictionary',
          component: () => import('@/pages/SystemManagement/DataDictionary/DataDictionary.vue'),
          meta: { ...defaultMeta, title: '数据字典' }
        }
      ]
    },
    {
      path: '/nofind',
      component: () => import('@/pages/NoFind.vue')
    },
    {
      path: '/:catchAll(.*)',
      redirect: '/nofind'
    }
  ]
})

export default router
