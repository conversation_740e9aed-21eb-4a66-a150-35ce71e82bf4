# API接口文档和数据结构

## 目录
- [接口概览](#接口概览)
- [认证机制](#认证机制)
- [数据源管理API](#数据源管理api)
- [预警管理API](#预警管理api)
- [系统管理API](#系统管理api)
- [数据结构定义](#数据结构定义)
- [错误码说明](#错误码说明)

## 接口概览

### 基础信息

- **Base URL**: `/alert-converge-api`
- **SSO URL**: `/sso-service`
- **Content-Type**: `application/json`
- **字符编码**: `UTF-8`

### 请求格式

所有API请求都遵循RESTful规范：

```http
GET /alert-converge-api/dataSource/page?page=1&size=10
POST /alert-converge-api/dataSource/save
PUT /alert-converge-api/dataSource/update
DELETE /alert-converge-api/dataSource/del/123
```

### 响应格式

```json
{
  "code": "0000",
  "message": "操作成功",
  "data": {},
  "timestamp": 1640995200000
}
```

## 认证机制

### Token认证

所有API请求需要在请求头中携带Token：

```http
Headers:
Token: your-auth-token-here
```

### 权限验证

系统基于角色的权限控制，不同角色可访问不同的API接口。

## 数据源管理API

### 1. 查询数据源列表

**接口地址**: `GET /dataSource/page`

**请求参数**:
```typescript
interface GetDataSourceParams {
  page: number          // 页码，从1开始
  size: number          // 每页大小
  systemName?: string   // 系统名称（可选）
  dataSourceType?: string // 数据源类型（可选）
}
```

**请求示例**:
```http
GET /alert-converge-api/dataSource/page?page=1&size=10&systemName=用户系统
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "systemName": "用户系统",
        "dataSourceName": "用户API",
        "dataSourceType": "HTTP",
        "url": "https://api.example.com/users",
        "method": "GET",
        "headers": "Content-Type: application/json",
        "params": "page=1&size=10",
        "description": "用户数据接口",
        "status": 1,
        "createTime": "2024-01-01 10:00:00",
        "updateTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 100,
    "current": 1,
    "size": 10
  }
}
```

### 2. 新增数据源

**接口地址**: `POST /dataSource/save`

**请求参数**:
```typescript
interface AddDataSourceParams {
  systemName: string      // 系统名称
  dataSourceName: string  // 数据源名称
  dataSourceType: string  // 数据源类型
  url: string            // 接口地址
  method: string         // 请求方法
  headers?: string       // 请求头
  params?: string        // 请求参数
  description?: string   // 描述
}
```

**请求示例**:
```json
{
  "systemName": "订单系统",
  "dataSourceName": "订单查询API",
  "dataSourceType": "HTTP",
  "url": "https://api.example.com/orders",
  "method": "GET",
  "headers": "Authorization: Bearer token",
  "description": "订单数据查询接口"
}
```

### 3. 更新数据源

**接口地址**: `POST /dataSource/update`

**请求参数**: 同新增接口，需额外包含id字段

### 4. 删除数据源

**接口地址**: `POST /dataSource/del/{id}`

**路径参数**:
- `id`: 数据源ID

## 预警管理API

### 1. 预警模型管理

#### 查询预警模型列表

**接口地址**: `GET /alertModel/page`

**请求参数**:
```typescript
interface GetAlertModelParams {
  page: number
  size: number
  modelName?: string      // 模型名称
  modelCode?: string      // 模型编号
  businessType?: string   // 业务类型
  alertType?: string      // 预警类型
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "modelName": "用户异常登录预警",
        "modelCode": "USER_LOGIN_ABNORMAL",
        "businessType": "security",
        "alertType": "warning",
        "description": "检测用户异常登录行为",
        "related": true,
        "createTime": "2024-01-01 10:00:00"
      }
    ],
    "total": 50,
    "current": 1,
    "size": 10
  }
}
```

#### 新增预警模型

**接口地址**: `POST /alertModel/save`

**请求参数**:
```typescript
interface AddAlertModelParams {
  modelName: string       // 模型名称
  modelCode: string       // 模型编号
  businessType: string    // 业务类型
  alertType: string       // 预警类型
  description?: string    // 模型说明
  rules?: AlertRule[]     // 预警规则
}
```

#### 更新预警模型

**接口地址**: `POST /alertModel/update`

#### 删除预警模型

**接口地址**: `POST /alertModel/del/{id}`

### 2. 预警事件管理

#### 查询预警事件列表

**接口地址**: `GET /alertEvent/page`

**请求参数**:
```typescript
interface GetAlertEventParams {
  page: number
  size: number
  systemName?: string     // 系统名称
  modelName?: string      // 模型名称
  alertLevel?: string     // 预警级别
  status?: string         // 处理状态
  startTime?: string      // 开始时间
  endTime?: string        // 结束时间
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": 1,
        "systemName": "用户系统",
        "modelName": "用户异常登录预警",
        "alertLevel": "high",
        "alertContent": "用户在异常时间段登录",
        "status": "pending",
        "createTime": "2024-01-01 10:00:00",
        "handleTime": null,
        "handler": null
      }
    ],
    "total": 200,
    "current": 1,
    "size": 10
  }
}
```

#### 导出预警事件

**接口地址**: `POST /alertEvent/export`

**请求参数**: 同查询接口

**响应**: 返回Excel文件流

### 3. 字典数据API

#### 获取所有系统名称

**接口地址**: `GET /dataSource/getAllSystemName`

**响应示例**:
```json
{
  "code": "0000",
  "message": "查询成功",
  "data": [
    {
      "value": "user-system",
      "label": "用户系统"
    },
    {
      "value": "order-system", 
      "label": "订单系统"
    }
  ]
}
```

#### 获取所有模型名称

**接口地址**: `GET /alertModel/getAllModelName`

#### 获取字典值

**接口地址**: `GET /sysDict/getSysDictValue`

**请求参数**:
```typescript
interface GetSysDictParams {
  keyName: string  // 字典键名，如：business_type, alert_type
}
```

**响应示例**:
```json
{
  "code": "0000",
  "message": "查询成功",
  "data": [
    {
      "value": "security",
      "label": "安全类"
    },
    {
      "value": "business",
      "label": "业务类"
    }
  ]
}
```

## 系统管理API

### 数据字典管理

#### 查询数据字典

**接口地址**: `GET /sysDict/page`

#### 新增数据字典

**接口地址**: `POST /sysDict/save`

#### 更新数据字典

**接口地址**: `POST /sysDict/update`

#### 删除数据字典

**接口地址**: `POST /sysDict/del/{id}`

## 数据结构定义

### 基础数据类型

```typescript
// 分页响应
interface PageResponse<T> {
  records: T[]
  total: number
  current: number
  size: number
}

// API响应
interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
  timestamp?: number
}

// 选项数据
interface Option {
  value: string | number
  label: string
}
```

### 业务数据类型

```typescript
// 数据源
interface DataSource {
  id: number
  systemName: string
  dataSourceName: string
  dataSourceType: string
  url: string
  method: string
  headers?: string
  params?: string
  description?: string
  status: number
  createTime: string
  updateTime: string
}

// 预警模型
interface AlertModel {
  id: number
  modelName: string
  modelCode: string
  businessType: string
  alertType: string
  description?: string
  related: boolean
  createTime: string
  updateTime: string
}

// 预警事件
interface AlertEvent {
  id: number
  systemName: string
  modelName: string
  alertLevel: string
  alertContent: string
  status: string
  createTime: string
  handleTime?: string
  handler?: string
}

// 预警规则
interface AlertRule {
  id: number
  ruleName: string
  ruleExpression: string
  threshold: number
  operator: string
  description?: string
}

// 数据字典
interface SysDict {
  id: number
  keyName: string
  keyValue: string
  keyLabel: string
  sort: number
  description?: string
  status: number
}
```

## 错误码说明

### 通用错误码

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 0000 | 成功 | 正常处理 |
| 300 | 网络错误或跨域 | 检查网络连接 |
| 401 | 无权限 | 联系管理员 |
| 403 | Token失效 | 重新登录 |
| 404 | 资源不存在 | 检查请求路径 |
| 500 | 服务器内部错误 | 联系技术支持 |

### 业务错误码

| 错误码 | 说明 | 处理方式 |
|--------|------|----------|
| 1001 | 参数校验失败 | 检查请求参数 |
| 1002 | 数据不存在 | 确认数据是否存在 |
| 1003 | 数据已存在 | 避免重复创建 |
| 1004 | 操作失败 | 重试或联系支持 |

### 错误处理示例

```typescript
// 统一错误处理
axios.interceptors.response.use(
  response => {
    const { code, message } = response.data
    
    switch (code) {
      case '0000':
        return response.data
      case '401':
        ElMessage.error('没有对应权限，请联系管理员')
        break
      case '403':
        ElMessage.error('token失效，请重新登录')
        router.push('/login')
        break
      case '404':
        ElMessage.error('该请求没有找到指定资源!')
        break
      default:
        ElMessage.error(message || '请求失败')
    }
    
    return Promise.reject(new Error(message))
  },
  error => {
    ElMessage.error('网络请求失败')
    return Promise.reject(error)
  }
)
```

## 接口调用示例

### JavaScript/TypeScript

```typescript
import http from '@/assets/js/http'

// 查询数据源列表
const getDataSources = async (params: GetDataSourceParams) => {
  try {
    const response = await http.get('/dataSource/page', params)
    return response.data
  } catch (error) {
    console.error('获取数据源失败:', error)
    throw error
  }
}

// 新增预警模型
const addAlertModel = async (model: AddAlertModelParams) => {
  try {
    const response = await http.post('/alertModel/save', model)
    return response.data
  } catch (error) {
    console.error('新增预警模型失败:', error)
    throw error
  }
}
```

### cURL示例

```bash
# 查询数据源列表
curl -X GET "https://alert.qmwallet.vip/alert-converge-api/dataSource/page?page=1&size=10" \
  -H "Token: your-auth-token" \
  -H "Content-Type: application/json"

# 新增预警模型
curl -X POST "https://alert.qmwallet.vip/alert-converge-api/alertModel/save" \
  -H "Token: your-auth-token" \
  -H "Content-Type: application/json" \
  -d '{
    "modelName": "测试模型",
    "modelCode": "TEST_MODEL",
    "businessType": "test",
    "alertType": "info",
    "description": "这是一个测试模型"
  }'
```
