import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'
export default {
  // API数据源-查询
  getDataSource: function (params: object) {
    return http.get(`${sysApi}/dataSource/page`, params)
  },
  // API数据源-删除
  delDataSource: function (params: any) {
    return http.post(`${sysApi}/dataSource/del/${params.id}`)
  },
  // API数据源-保存
  saveDataSource: function (params: object) {
    return http.post(`${sysApi}/dataSource/save`, params)
  },
  // API数据源-修改
  updateDataSource: function (params: object) {
    return http.post(`${sysApi}/dataSource/update`, params)
  },
  // 预警模型-查询
  getAlertModel: function (params: object) {
    return http.get(`${sysApi}/alertModel/page`, params)
  },
  // 预警模型-删除
  delAlertModel: function (params: any) {
    return http.post(`${sysApi}/alertModel/del/${params.id}`)
  },
  // 预警模型-保存
  addAlertModel: function (params: object) {
    return http.post(`${sysApi}/alertModel/save`, params)
  },
  // 预警模型-修改
  editAlertModel: function (params: object) {
    return http.post(`${sysApi}/alertModel/update`, params)
  },
  // 预警事件-查询
  getAlertEvent: function (params: object) {
    return http.get(`${sysApi}/alertEvent/page`, params)
  },
  // 预警事件-导出
  exportAlertEvent: function (params: object) {
    return http.post(`${sysApi}/alertEvent/export`, params)
  },
  // 获取所有系统名称列表
  getAllSystemName: function () {
    return http.get(`${sysApi}/dataSource/getAllSystemName`)
  },
  // 获取所有预警名称列表
  getAllModelName: function () {
    return http.get(`${sysApi}/alertModel/getAllModelName`)
  },
  // 获取业务类型、预警类型列表
  getSysDictValue: function (params: any) {
    return http.get(`${sysApi}/sysDictValue/list/${params.keyName}`)
  },
  // 预警消息-查询
  getAlertMessage: function (params: object) {
    return http.get(`${sysApi}/alertMessage/page`, params)
  },
  // 预警消息-导出
  exportAlertMessage: function (params: object) {
    return http.post(`${sysApi}/alertMessage/export`, params)
  },
  // 预警消息-处理
  processAlertMessage: function (params: object) {
    return http.post(`${sysApi}/alertMessage/process`, params)
  },
  // 预警消息-忽略
  ignoreAlertMessage: function (params: object) {
    return http.post(`${sysApi}/alertMessage/ignore`, params)
  },
  // 搜索用户
  searchUsers: function (params: object) {
    return http.get(`${sysApi}/user/search`, params)
  }
}
