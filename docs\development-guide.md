# 开发规范和最佳实践

## 目录
- [开发环境配置](#开发环境配置)
- [代码规范](#代码规范)
- [组件开发规范](#组件开发规范)
- [API开发规范](#api开发规范)
- [状态管理规范](#状态管理规范)
- [样式开发规范](#样式开发规范)
- [最佳实践](#最佳实践)

## 开发环境配置

### 必需工具

1. **Node.js**: >= 16.0.0
2. **包管理器**: npm >= 8.0.0 或 yarn >= 1.22.0
3. **IDE**: 推荐 VS Code
4. **浏览器**: Chrome/Firefox 最新版本

### VS Code 推荐插件

```json
{
  "recommendations": [
    "Vue.volar",                    // Vue3语言支持
    "Vue.vscode-typescript-vue-plugin", // Vue TS支持
    "bradlc.vscode-tailwindcss",    // CSS智能提示
    "esbenp.prettier-vscode",       // 代码格式化
    "dbaeumer.vscode-eslint",       // ESLint支持
    "ms-vscode.vscode-typescript-next" // TypeScript支持
  ]
}
```

### 环境变量配置

创建环境配置文件：

```bash
# .env.dev (开发环境)
VITE_APP_TITLE=预警后台管理系统-开发
VITE_API_BASE_URL=http://localhost:8080
VITE_SSO_BASE_URL=http://sso.qmwallet.vip

# .env.test (测试环境)
VITE_APP_TITLE=预警后台管理系统-测试
VITE_API_BASE_URL=https://alert.qmwallet.vip
VITE_SSO_BASE_URL=http://sso.qmwallet.vip

# .env.pro (生产环境)
VITE_APP_TITLE=预警后台管理系统
VITE_API_BASE_URL=https://alert.qmwallet.vip
VITE_SSO_BASE_URL=http://sso.qmwallet.vip
```

## 代码规范

### TypeScript 规范

#### 1. 类型定义
```typescript
// ✅ 推荐：明确的接口定义
interface AlertModel {
  id: number
  modelName: string
  modelCode: string
  businessType: string
  alertType: string
  description?: string  // 可选属性使用?
  createTime: Date
}

// ✅ 推荐：泛型约束
interface ApiResponse<T = any> {
  code: string
  message: string
  data: T
}

// ❌ 避免：使用any类型
const data: any = response.data
```

#### 2. 函数定义
```typescript
// ✅ 推荐：明确的参数和返回值类型
async function fetchAlertModels(params: {
  page: number
  size: number
  modelName?: string
}): Promise<ApiResponse<AlertModel[]>> {
  return await http.get('/alertModel/page', params)
}

// ✅ 推荐：使用箭头函数
const handleDelete = async (id: number): Promise<void> => {
  await WarnApi.delAlertModel({ id })
  await getList()
}
```

#### 3. 组件类型定义
```typescript
// ✅ 推荐：Props接口定义
interface Props {
  dialogVisible: boolean
  dialogForm: AlertModel
  dialogType: 1 | 2  // 1-添加 2-编辑
}

// ✅ 推荐：Emits定义
interface Emits {
  (e: 'update:dialogVisible', value: boolean): void
  (e: 'callback'): void
}

// 组件中使用
const props = defineProps<Props>()
const emit = defineEmits<Emits>()
```

### ESLint 配置

项目使用的ESLint规则：

```javascript
// .eslintrc.js
module.exports = {
  extends: [
    '@vue/eslint-config-typescript',
    '@vue/eslint-config-prettier',
    'plugin:vue/vue3-recommended'
  ],
  rules: {
    // Vue相关
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'off',
    
    // TypeScript相关
    '@typescript-eslint/no-explicit-any': 'warn',
    '@typescript-eslint/no-unused-vars': 'error',
    
    // 通用规则
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off'
  }
}
```

### Prettier 配置

```json
{
  "semi": false,
  "singleQuote": true,
  "tabWidth": 2,
  "trailingComma": "none",
  "printWidth": 100,
  "endOfLine": "lf"
}
```

## 组件开发规范

### 1. 组件命名

```typescript
// ✅ 推荐：PascalCase命名
export default defineComponent({
  name: 'AlertModelDialog'
})

// ✅ 推荐：文件名与组件名一致
// 文件：AlertModelDialog.vue
// 组件：AlertModelDialog
```

### 2. 组件结构

```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
// 1. 导入依赖
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import WarnApi from '@/api/EarlyWarnManage'

// 2. 类型定义
interface FormData {
  modelName: string
  modelCode: string
}

// 3. Props和Emits
interface Props {
  visible: boolean
}
const props = defineProps<Props>()
const emit = defineEmits<{
  'update:visible': [value: boolean]
}>()

// 4. 响应式数据
const loading = ref(false)
const formData = reactive<FormData>({
  modelName: '',
  modelCode: ''
})

// 5. 计算属性
const isValid = computed(() => {
  return formData.modelName && formData.modelCode
})

// 6. 方法定义
const handleSubmit = async () => {
  if (!isValid.value) return
  
  try {
    loading.value = true
    await WarnApi.addAlertModel(formData)
    ElMessage.success('保存成功')
    emit('update:visible', false)
  } catch (error) {
    ElMessage.error('保存失败')
  } finally {
    loading.value = false
  }
}

// 7. 生命周期
onMounted(() => {
  console.log('组件已挂载')
})
</script>

<style lang="scss" scoped>
/* 组件样式 */
</style>
```

### 3. 组件通信

```typescript
// ✅ 推荐：Props传递数据
// 父组件
<AlertDialog :model-data="selectedModel" />

// 子组件
interface Props {
  modelData: AlertModel
}
const props = defineProps<Props>()

// ✅ 推荐：Emit触发事件
// 子组件
const emit = defineEmits<{
  'model-updated': [model: AlertModel]
}>()

emit('model-updated', updatedModel)

// 父组件
<AlertDialog @model-updated="handleModelUpdate" />
```

## API开发规范

### 1. API文件组织

```typescript
// src/api/EarlyWarnManage.ts
import { sysApi } from '@/globalSettings'
import http from '@/assets/js/http'

export default {
  // 预警模型相关API
  getAlertModel: (params: GetAlertModelParams) => 
    http.get(`${sysApi}/alertModel/page`, params),
    
  addAlertModel: (params: AddAlertModelParams) => 
    http.post(`${sysApi}/alertModel/save`, params),
    
  updateAlertModel: (params: UpdateAlertModelParams) => 
    http.post(`${sysApi}/alertModel/update`, params),
    
  deleteAlertModel: (id: number) => 
    http.post(`${sysApi}/alertModel/del/${id}`)
}

// 类型定义
export interface GetAlertModelParams {
  page: number
  size: number
  modelName?: string
  businessType?: string
}

export interface AddAlertModelParams {
  modelName: string
  modelCode: string
  businessType: string
  alertType: string
  description?: string
}
```

### 2. HTTP请求封装

```typescript
// src/assets/js/http.ts
import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router'

// 请求拦截器
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => Promise.reject(error)
)

// 响应拦截器
axios.interceptors.response.use(
  response => {
    const { code, message } = response.data
    
    if (code !== '0000') {
      ElMessage.error(message || '请求失败')
      
      if (code === '403') {
        router.push('/login')
      }
      
      return Promise.reject(new Error(message))
    }
    
    return response.data
  },
  error => {
    ElMessage.error('网络请求失败')
    return Promise.reject(error)
  }
)

export default {
  get: <T = any>(url: string, params?: any): Promise<T> =>
    axios.get(url, { params }),
    
  post: <T = any>(url: string, data?: any): Promise<T> =>
    axios.post(url, data),
    
  put: <T = any>(url: string, data?: any): Promise<T> =>
    axios.put(url, data),
    
  delete: <T = any>(url: string): Promise<T> =>
    axios.delete(url)
}
```

## 状态管理规范

### 1. Store定义

```typescript
// src/stores/alertStore.ts
import { defineStore } from 'pinia'
import WarnApi from '@/api/EarlyWarnManage'

interface AlertState {
  systemNameOptions: SystemOption[]
  modelNameOptions: ModelOption[]
  loading: boolean
}

export const useAlertStore = defineStore('alert', {
  state: (): AlertState => ({
    systemNameOptions: [],
    modelNameOptions: [],
    loading: false
  }),
  
  getters: {
    getSystemById: (state) => (id: number) =>
      state.systemNameOptions.find(item => item.id === id),
      
    hasSystemOptions: (state) => state.systemNameOptions.length > 0
  },
  
  actions: {
    async fetchSystemNames() {
      try {
        this.loading = true
        const res = await WarnApi.getAllSystemName()
        this.systemNameOptions = res.data
      } catch (error) {
        console.error('获取系统名称失败:', error)
      } finally {
        this.loading = false
      }
    },
    
    clearSystemNames() {
      this.systemNameOptions = []
    }
  }
})
```

### 2. Store使用

```typescript
// 在组件中使用
import { useAlertStore } from '@/stores/alertStore'

export default defineComponent({
  setup() {
    const alertStore = useAlertStore()
    
    // 访问状态
    const systemOptions = computed(() => alertStore.systemNameOptions)
    
    // 调用actions
    const loadData = async () => {
      await alertStore.fetchSystemNames()
    }
    
    onMounted(() => {
      loadData()
    })
    
    return {
      systemOptions,
      loadData
    }
  }
})
```

## 样式开发规范

### 1. SCSS组织结构

```scss
// src/assets/css/base.scss
@import './mixin.scss';
@import './theme/handleThemes.scss';

// 全局样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', Arial, sans-serif;
  font-size: 14px;
  color: #333;
}

// 工具类
.flex_row {
  display: flex;
  flex-direction: row;
}

.flex_center {
  justify-content: center;
  align-items: center;
}

.mb10 {
  margin-bottom: 10px;
}

.mt20 {
  margin-top: 20px;
}
```

### 2. 组件样式规范

```vue
<style lang="scss" scoped>
// 使用BEM命名规范
.alert-dialog {
  &__header {
    padding: 20px;
    border-bottom: 1px solid #eee;
  }
  
  &__content {
    padding: 20px;
  }
  
  &__footer {
    padding: 20px;
    text-align: right;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .alert-dialog {
    &__content {
      padding: 10px;
    }
  }
}

// 深度选择器（修改第三方组件样式）
:deep(.el-dialog__header) {
  background-color: #f5f5f5;
}
</style>
```

### 3. 主题变量

```scss
// src/assets/css/theme/themes.scss
:root {
  // 主色调
  --primary-color: #409eff;
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  
  // 文本颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;
  
  // 边框颜色
  --border-base: #dcdfe6;
  --border-light: #e4e7ed;
  --border-lighter: #ebeef5;
  --border-extra-light: #f2f6fc;
  
  // 背景颜色
  --bg-color: #ffffff;
  --bg-color-page: #f2f3f5;
  --bg-color-overlay: #ffffff;
}
```

## 最佳实践

### 1. 性能优化

```typescript
// ✅ 使用defineAsyncComponent懒加载组件
const AlertDialog = defineAsyncComponent(() => 
  import('./components/AlertDialog.vue')
)

// ✅ 使用shallowRef优化大对象
import { shallowRef } from 'vue'
const largeData = shallowRef(bigObject)

// ✅ 使用v-memo优化列表渲染
<template>
  <div v-for="item in list" :key="item.id" v-memo="[item.id, item.status]">
    {{ item.name }}
  </div>
</template>
```

### 2. 错误处理

```typescript
// ✅ 统一错误处理
const handleAsyncOperation = async () => {
  try {
    loading.value = true
    const result = await someAsyncOperation()
    // 处理成功结果
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error(error.message || '操作失败')
  } finally {
    loading.value = false
  }
}

// ✅ 全局错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('全局错误:', err, info)
  // 上报错误到监控系统
}
```

### 3. 代码复用

```typescript
// ✅ 使用Composables复用逻辑
// composables/useTable.ts
export function useTable<T>(api: Function) {
  const tableData = ref<T[]>([])
  const loading = ref(false)
  const pagination = reactive({
    currentPage: 1,
    pageSize: 10,
    total: 0
  })
  
  const getList = async (page = 1) => {
    try {
      loading.value = true
      pagination.currentPage = page
      
      const res = await api({
        page: pagination.currentPage,
        size: pagination.pageSize
      })
      
      tableData.value = res.data.records
      pagination.total = res.data.total
    } catch (error) {
      ElMessage.error('获取数据失败')
    } finally {
      loading.value = false
    }
  }
  
  return {
    tableData,
    loading,
    pagination,
    getList
  }
}

// 在组件中使用
const { tableData, loading, pagination, getList } = useTable(WarnApi.getAlertModel)
```

### 4. 测试规范

```typescript
// ✅ 单元测试示例
import { mount } from '@vue/test-utils'
import AlertDialog from '@/components/AlertDialog.vue'

describe('AlertDialog', () => {
  it('should render correctly', () => {
    const wrapper = mount(AlertDialog, {
      props: {
        visible: true,
        modelData: mockData
      }
    })
    
    expect(wrapper.find('.alert-dialog').exists()).toBe(true)
  })
  
  it('should emit close event', async () => {
    const wrapper = mount(AlertDialog)
    await wrapper.find('.close-btn').trigger('click')
    
    expect(wrapper.emitted('update:visible')).toBeTruthy()
  })
})
```

### 5. 文档规范

```typescript
/**
 * 预警模型对话框组件
 * @description 用于添加和编辑预警模型的对话框
 * <AUTHOR>
 * @date 2024-01-01
 * @example
 * <AlertDialog 
 *   v-model:visible="dialogVisible"
 *   :model-data="selectedModel"
 *   @model-updated="handleUpdate"
 * />
 */
export default defineComponent({
  name: 'AlertDialog'
  // ...
})
```
