# 构建和部署指南

## 目录
- [环境准备](#环境准备)
- [本地开发](#本地开发)
- [构建配置](#构建配置)
- [部署方式](#部署方式)
- [环境配置](#环境配置)
- [性能优化](#性能优化)
- [监控和日志](#监控和日志)

## 环境准备

### 开发环境要求

- **Node.js**: >= 16.0.0
- **npm**: >= 8.0.0 或 **yarn**: >= 1.22.0
- **Git**: 最新版本

### 环境检查

```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 检查yarn版本（如果使用yarn）
yarn --version
```

## 本地开发

### 1. 克隆项目

```bash
git clone <repository-url>
cd warning-system-pc
```

### 2. 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 启动开发服务器

```bash
# 开发环境
npm run dev

# 测试环境
npm run test
```

访问 http://localhost:5173 查看应用

### 4. 开发环境配置

创建 `.env.local` 文件（不会被提交到版本控制）：

```bash
# .env.local
VITE_APP_TITLE=预警后台管理系统-本地开发
VITE_API_BASE_URL=http://localhost:8080
VITE_SSO_BASE_URL=http://localhost:3000
```

## 构建配置

### 构建脚本说明

```json
{
  "scripts": {
    "dev": "vite --mode dev",           // 开发环境
    "test": "vite --mode test",         // 测试环境预览
    "build:test": "vite build --mode test",  // 测试环境构建
    "build:pro": "vite build --mode pro",    // 生产环境构建
    "build": "run-p type-check build-only",  // 完整构建（类型检查+构建）
    "preview": "vite preview",          // 预览构建结果
    "type-check": "vue-tsc --noEmit",   // TypeScript类型检查
    "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix",
    "format": "prettier --write src/"   // 代码格式化
  }
}
```

### 环境配置文件

#### .env.dev (开发环境)
```bash
NODE_ENV=development
VITE_APP_TITLE=预警后台管理系统-开发
VITE_API_BASE_URL=http://localhost:8080
VITE_SSO_BASE_URL=http://sso.qmwallet.vip
```

#### .env.test (测试环境)
```bash
NODE_ENV=production
VITE_APP_TITLE=预警后台管理系统-测试
VITE_API_BASE_URL=https://alert.qmwallet.vip
VITE_SSO_BASE_URL=http://sso.qmwallet.vip
```

#### .env.pro (生产环境)
```bash
NODE_ENV=production
VITE_APP_TITLE=预警后台管理系统
VITE_API_BASE_URL=https://alert.qmwallet.vip
VITE_SSO_BASE_URL=http://sso.qmwallet.vip
```

### Vite构建配置

```typescript
// vite.config.ts
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { fileURLToPath, URL } from 'node:url'

export default defineConfig(({ mode }) => {
  return {
    plugins: [vue()],
    base: './',
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      sourcemap: mode !== 'pro', // 生产环境不生成sourcemap
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: mode === 'pro', // 生产环境移除console
          drop_debugger: true
        }
      },
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          manualChunks: {
            vendor: ['vue', 'vue-router', 'pinia'],
            elementPlus: ['element-plus'],
            utils: ['axios', 'dayjs']
          }
        }
      }
    },
    server: {
      host: '0.0.0.0',
      port: 5173,
      proxy: {
        '^/sso-service': {
          target: 'http://sso.qmwallet.vip',
          changeOrigin: true
        },
        '^/alert-converge-api': {
          target: 'https://alert.qmwallet.vip',
          changeOrigin: true
        }
      }
    }
  }
})
```

## 部署方式

### 1. 静态文件部署

#### 构建项目

```bash
# 测试环境构建
npm run build:test

# 生产环境构建
npm run build:pro
```

构建完成后，`dist` 目录包含所有静态文件。

#### Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/warning-system-pc/dist;
    index index.html;

    # 处理Vue Router的history模式
    location / {
        try_files $uri $uri/ /index.html;
    }

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # API代理
    location /sso-service/ {
        proxy_pass http://sso.qmwallet.vip/sso-service/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    location /alert-converge-api/ {
        proxy_pass https://alert.qmwallet.vip/alert-converge-api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # Gzip压缩
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;
}
```

### 2. Docker部署

#### Dockerfile

```dockerfile
# 构建阶段
FROM node:16-alpine as build-stage

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build:pro

# 生产阶段
FROM nginx:alpine as production-stage

# 复制构建结果
COPY --from=build-stage /app/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/conf.d/default.conf

EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

#### docker-compose.yml

```yaml
version: '3.8'

services:
  warning-system-pc:
    build: .
    ports:
      - "80:80"
    environment:
      - NODE_ENV=production
    volumes:
      - ./nginx.conf:/etc/nginx/conf.d/default.conf
    restart: unless-stopped
```

#### 构建和运行

```bash
# 构建镜像
docker build -t warning-system-pc .

# 运行容器
docker run -d -p 80:80 --name warning-system-pc warning-system-pc

# 使用docker-compose
docker-compose up -d
```

### 3. CI/CD部署

#### GitHub Actions示例

```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '16'
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run tests
      run: npm run test
    
    - name: Type check
      run: npm run type-check
    
    - name: Lint
      run: npm run lint
    
    - name: Build
      run: npm run build:pro
    
    - name: Deploy to server
      uses: appleboy/ssh-action@v0.1.5
      with:
        host: ${{ secrets.HOST }}
        username: ${{ secrets.USERNAME }}
        key: ${{ secrets.SSH_KEY }}
        script: |
          cd /var/www/warning-system-pc
          git pull origin main
          npm ci
          npm run build:pro
          sudo systemctl reload nginx
```

## 环境配置

### 环境变量管理

```typescript
// src/config/env.ts
interface EnvConfig {
  APP_TITLE: string
  API_BASE_URL: string
  SSO_BASE_URL: string
}

const getEnvConfig = (): EnvConfig => {
  return {
    APP_TITLE: import.meta.env.VITE_APP_TITLE || '预警后台管理系统',
    API_BASE_URL: import.meta.env.VITE_API_BASE_URL || '',
    SSO_BASE_URL: import.meta.env.VITE_SSO_BASE_URL || ''
  }
}

export default getEnvConfig()
```

### 配置验证

```typescript
// src/config/validate.ts
const validateEnv = () => {
  const requiredEnvs = [
    'VITE_API_BASE_URL',
    'VITE_SSO_BASE_URL'
  ]
  
  const missingEnvs = requiredEnvs.filter(env => !import.meta.env[env])
  
  if (missingEnvs.length > 0) {
    throw new Error(`Missing required environment variables: ${missingEnvs.join(', ')}`)
  }
}

export default validateEnv
```

## 性能优化

### 1. 构建优化

```typescript
// vite.config.ts 优化配置
export default defineConfig({
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // 将大型库分离到单独的chunk
          vendor: ['vue', 'vue-router', 'pinia'],
          elementPlus: ['element-plus'],
          utils: ['axios', 'dayjs', 'lodash-es']
        }
      }
    },
    // 启用压缩
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    }
  }
})
```

### 2. 资源优化

```typescript
// 路由懒加载
const routes = [
  {
    path: '/early-warn',
    component: () => import('@/pages/EarlyWarnManagement/EarlyWarningModel/EarlyWarningModel.vue')
  }
]

// 组件懒加载
const AlertDialog = defineAsyncComponent(() => 
  import('@/components/AlertDialog.vue')
)
```

### 3. 缓存策略

```nginx
# Nginx缓存配置
location ~* \.(js|css)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
}

location ~* \.(png|jpg|jpeg|gif|ico|svg)$ {
    expires 6M;
    add_header Cache-Control "public";
}

location /index.html {
    expires -1;
    add_header Cache-Control "no-cache, no-store, must-revalidate";
}
```

## 监控和日志

### 1. 错误监控

```typescript
// src/utils/errorHandler.ts
import { ElMessage } from 'element-plus'

// 全局错误处理
window.addEventListener('error', (event) => {
  console.error('全局错误:', event.error)
  // 发送错误到监控系统
  sendErrorToMonitoring({
    message: event.error.message,
    stack: event.error.stack,
    url: window.location.href,
    timestamp: new Date().toISOString()
  })
})

// Vue错误处理
app.config.errorHandler = (err, vm, info) => {
  console.error('Vue错误:', err, info)
  ElMessage.error('系统出现错误，请刷新页面重试')
}
```

### 2. 性能监控

```typescript
// src/utils/performance.ts
// 页面加载性能监控
window.addEventListener('load', () => {
  const perfData = performance.getEntriesByType('navigation')[0]
  const loadTime = perfData.loadEventEnd - perfData.fetchStart
  
  console.log('页面加载时间:', loadTime)
  
  // 发送性能数据到监控系统
  sendPerformanceData({
    loadTime,
    domContentLoaded: perfData.domContentLoadedEventEnd - perfData.fetchStart,
    firstPaint: performance.getEntriesByType('paint')[0]?.startTime
  })
})
```

### 3. 日志管理

```typescript
// src/utils/logger.ts
enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3
}

class Logger {
  private level: LogLevel = LogLevel.INFO
  
  debug(message: string, ...args: any[]) {
    if (this.level <= LogLevel.DEBUG) {
      console.debug(`[DEBUG] ${message}`, ...args)
    }
  }
  
  info(message: string, ...args: any[]) {
    if (this.level <= LogLevel.INFO) {
      console.info(`[INFO] ${message}`, ...args)
    }
  }
  
  warn(message: string, ...args: any[]) {
    if (this.level <= LogLevel.WARN) {
      console.warn(`[WARN] ${message}`, ...args)
    }
  }
  
  error(message: string, ...args: any[]) {
    if (this.level <= LogLevel.ERROR) {
      console.error(`[ERROR] ${message}`, ...args)
    }
  }
}

export default new Logger()
```

## 部署检查清单

### 构建前检查

- [ ] 环境变量配置正确
- [ ] 依赖版本兼容
- [ ] TypeScript类型检查通过
- [ ] ESLint检查通过
- [ ] 单元测试通过

### 部署前检查

- [ ] 构建成功
- [ ] 静态资源路径正确
- [ ] API代理配置正确
- [ ] 缓存策略配置
- [ ] 错误页面配置

### 部署后检查

- [ ] 页面正常访问
- [ ] API接口正常
- [ ] 登录功能正常
- [ ] 核心功能测试
- [ ] 性能指标正常
- [ ] 错误监控正常

## 常见问题

### 1. 构建失败

```bash
# 清除缓存重新构建
rm -rf node_modules package-lock.json
npm install
npm run build
```

### 2. 路由404问题

确保服务器配置了正确的fallback规则：

```nginx
location / {
    try_files $uri $uri/ /index.html;
}
```

### 3. 静态资源加载失败

检查 `vite.config.ts` 中的 `base` 配置：

```typescript
export default defineConfig({
  base: './', // 相对路径
  // 或
  base: '/warning-system/', // 绝对路径
})
```

### 4. 跨域问题

确保API服务器配置了正确的CORS头：

```javascript
// 服务器端配置
app.use(cors({
  origin: ['http://localhost:5173', 'https://your-domain.com'],
  credentials: true
}))
```
